<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

/**
 * Clase Partner para manejar los socios/partners del sistema.
 * Maneja información de prioridad e imagen de los partners.
 */
class Partner
{
    // --- Atributos ---
    private ?int    $id        = null;
    private ?int    $prioridad = null;
    private ?string $imagen    = null;
    private ?int    $estado    = null;

    /**
     * Constructor: Inicializa las propiedades del objeto Partner.
     */
    public function __construct()
    {
        $this->id        = 0;
        $this->prioridad = null;
        $this->imagen    = null;
        $this->estado    = 1; // Estado activo por defecto
    }

    /**
     * Método estático para construir un objeto Partner desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos del partner.
     *
     * @return self Instancia de Partner.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto            = new self();
            $objeto->id        = isset($resultado['id']) ? (int)$resultado['id'] : 0;
            $objeto->prioridad = isset($resultado['prioridad']) ? (int)$resultado['prioridad'] : null;
            $objeto->imagen    = $resultado['imagen'] ?? null;
            $objeto->estado    = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
            return $objeto;
        } catch (Exception $e) {
            throw new Exception("Error al construir Partner: " . $e->getMessage());
        }
    }

    // --- Métodos de Acceso a Datos (Estáticos) ---

    /**
     * Obtiene un partner por su ID.
     *
     * @param int $id       ID del partner.
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto Partner o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public static function get(int $id, PDO $conexion): ?self
    {
        try {
            $query = <<<SQL
            SELECT
                p.*
            FROM partners p
            WHERE
                p.id = :id
                AND p.estado = 1
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener Partner: " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de partners ordenados por prioridad.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos Partner.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                p.*
            FROM partners p
            WHERE
                p.estado = 1
            ORDER BY
                p.prioridad ASC, p.id ASC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista de Partners: " . $e->getMessage());
        }
    }

    /**
     * Crea un nuevo partner en la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID del nuevo partner creado o false en caso de error.
     * @throws Exception Si los datos requeridos están vacíos o hay error en DB.
     */
    public function crear(PDO $conexion): int|false
    {
        // Validaciones básicas sobre el objeto
        if ($this->getPrioridad() === null) {
            throw new Exception("La prioridad es requerida para crear el partner.");
        }

        // Validaciones de negocio
        $this->validarReglasNegocio($conexion);

        try {
            $query = <<<SQL
            INSERT INTO partners (
                 prioridad
                ,imagen
                ,estado
            ) VALUES (
                 :prioridad
                ,:imagen
                ,:estado
            )
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':prioridad', $this->getPrioridad(), PDO::PARAM_INT);
            $statement->bindValue(':imagen', $this->getImagen(), PDO::PARAM_STR);
            $statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);

            $success = $statement->execute();

            if ($success) {
                return (int)$conexion->lastInsertId();
            } else {
                return false;
            }

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al crear partner: " . $e->getMessage());
        } catch (Exception $e) {
            throw new Exception("Error al crear partner: " . $e->getMessage());
        }
    }

    /**
     * Modifica un partner existente.
     *
     * @param int    $id         ID del partner a modificar.
     * @param int    $prioridad  Nueva prioridad.
     * @param string $imagen     Nueva imagen.
     * @param PDO    $conexion   Conexión PDO.
     *
     * @return bool True si la modificación fue exitosa, False en caso contrario.
     * @throws Exception Si los datos no son válidos o si ocurre un error de base de datos.
     */
    public static function modificar(int $id, int $prioridad, ?string $imagen, PDO $conexion): bool
    {
        if ($id <= 0) {
            throw new Exception("ID de partner no válido.");
        }

        if ($prioridad < 0) {
            throw new Exception("La prioridad no puede ser negativa.");
        }

        try {
            $query = <<<SQL
            UPDATE partners SET
                prioridad = :prioridad,
                imagen = :imagen
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':prioridad', $prioridad, PDO::PARAM_INT);
            $statement->bindValue(':imagen', $imagen, PDO::PARAM_STR);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al modificar partner (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Desactiva un partner (soft delete).
     *
     * @param int $id       ID del partner a desactivar.
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la desactivación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function desactivar(int $id, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            UPDATE partners SET
                estado = 0
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al desactivar partner (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Elimina físicamente un partner de la base de datos.
     * NOTA: Se recomienda usar desactivar() en su lugar para soft delete.
     *
     * @param int $id       ID del partner a eliminar.
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la eliminación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function eliminar(int $id, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            DELETE FROM partners
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al eliminar partner (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Valida que no exista otro partner activo con la misma prioridad (para edición).
     *
     * @param int $prioridad Prioridad a validar.
     * @param int $excludeId ID del partner a excluir de la validación.
     * @param PDO $conexion  Conexión PDO.
     *
     * @throws Exception Si ya existe otro partner con la misma prioridad.
     */
    public static function validarPrioridadUnica(int $prioridad, int $excludeId, PDO $conexion): void
    {
        $query = "SELECT COUNT(*) FROM partners WHERE prioridad = :prioridad AND estado = 1 AND id != :exclude_id";
        $statement = $conexion->prepare($query);
        $statement->bindValue(':prioridad', $prioridad, PDO::PARAM_INT);
        $statement->bindValue(':exclude_id', $excludeId, PDO::PARAM_INT);
        $statement->execute();

        if ($statement->fetchColumn() > 0) {
            throw new Exception("Ya existe otro partner activo con la prioridad {$prioridad}.");
        }
    }

    /**
     * Valida las reglas de negocio para la creación de partners.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @throws Exception Si las validaciones fallan.
     */
    private function validarReglasNegocio(PDO $conexion): void
    {
        // Validar que la prioridad no sea negativa
        if ($this->getPrioridad() !== null && $this->getPrioridad() < 0) {
            throw new Exception("La prioridad no puede ser negativa.");
        }

        // Validar que no exista otro partner activo con la misma prioridad
        if ($this->getPrioridad() !== null) {
            $query = "SELECT COUNT(*) FROM partners WHERE prioridad = :prioridad AND estado = 1";
            $statement = $conexion->prepare($query);
            $statement->bindValue(':prioridad', $this->getPrioridad(), PDO::PARAM_INT);
            $statement->execute();

            if ($statement->fetchColumn() > 0) {
                throw new Exception("Ya existe un partner activo con la prioridad {$this->getPrioridad()}.");
            }
        }
    }

    // --- Getters y Setters ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getPrioridad(): ?int
    {
        return $this->prioridad;
    }

    public function setPrioridad(?int $prioridad): self
    {
        $this->prioridad = $prioridad;
        return $this;
    }

    public function getImagen(): ?string
    {
        return $this->imagen;
    }

    public function setImagen(?string $imagen): self
    {
        $this->imagen = $imagen;
        return $this;
    }

    public function getEstado(): ?int
    {
        return $this->estado;
    }

    public function setEstado(?int $estado): self
    {
        $this->estado = $estado;
        return $this;
    }

    // --- Métodos adicionales ---

    /**
     * Verifica si el partner está activo.
     * @return bool
     */
    public function isActivo(): bool
    {
        return $this->estado === 1;
    }
}
