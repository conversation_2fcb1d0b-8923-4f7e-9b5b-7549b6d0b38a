<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

// Import PDOException for better error handling

class Estrategia
{
	// --- Atributos ---
	private ?int    $id        = null;
	private ?string $titulo    = null;
	private ?string $texto     = null;
	private ?int    $prioridad = null;
	private ?int    $estado    = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto Estrategia.
	 */
	public function __construct()
	{
		$this->id        = 0; // O null si prefieres no usar 0 por defecto
		$this->titulo    = null;
		$this->texto     = null;
		$this->prioridad = null;
		$this->estado    = 1; // Estado activo por defecto
	}

	/**
	 * Método estático para construir un objeto Estrategia desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos de la estrategia.
	 *
	 * @return self Instancia de Estrategia.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto            = new self();
			$objeto->id        = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->titulo    = $resultado['titulo'] ?? null;
			$objeto->texto     = $resultado['texto'] ?? null;
			$objeto->prioridad = isset($resultado['prioridad']) ? (int)$resultado['prioridad'] : null;
			$objeto->estado    = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir Estrategia: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Obtiene una estrategia por su ID.
	 *
	 * @param int $id       ID de la estrategia.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Estrategia o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener estrategia por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	e.*
            FROM estrategias e
            WHERE
            	e.id = :id
            LIMIT 1
            SQL;
			// Asume tabla 'estrategias'

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Estrategia (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de estrategias activas ordenadas por prioridad.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Estrategia.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de estrategias activas ordenadas por prioridad (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	e.*
            FROM estrategias e
            WHERE
            	e.estado = 1
            ORDER BY
            	e.prioridad ASC,
            	e.titulo ASC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Estrategias: " . $e->getMessage());
		}
	}

	/**
	 * Crea una nueva estrategia en la base de datos a partir de un objeto Estrategia.
	 * El objeto Estrategia debe estar completamente poblado con al menos el título.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID de la nueva estrategia creada o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getTitulo())) {
			throw new Exception("El título es requerido en el objeto Estrategia para crearlo.");
		}

		// Validaciones de negocio
		$this->validarReglasNegocio($conexion);

		try {
			$titulo = $this->getTitulo(); // Para usar en mensaje de error

			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO estrategias (
            	 titulo
            	,texto
            	,prioridad
            	,estado
            ) VALUES (
            	 :titulo
            	,:texto
            	,:prioridad
            	,:estado
            )
            SQL;
			// Asume tabla 'estrategias'

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':titulo', $this->getTitulo(), PDO::PARAM_STR);
			$statement->bindValue(':texto', $this->getTexto(), PDO::PARAM_STR);
			$statement->bindValue(':prioridad', $this->getPrioridad(), PDO::PARAM_INT);
			$statement->bindValue(':estado', $this->getEstado() ?? 1, PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID de la estrategia recién creada
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			// Manejar errores específicos de DB
			throw new Exception("Error de base de datos al crear estrategia: " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear estrategia: " . $e->getMessage());
		}
	}

	/**
	 * Modifica una estrategia existente.
	 *
	 * @param int         $id        ID de la estrategia a modificar.
	 * @param string      $titulo    Nuevo título para la estrategia.
	 * @param string|null $texto     Nuevo texto para la estrategia (opcional).
	 * @param int|null    $prioridad Nueva prioridad para la estrategia (opcional).
	 * @param PDO         $conexion  Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si el título está vacío o si ocurre un error de base de datos.
	 */
	public static function modificar(int $id, string $titulo, ?string $texto, ?int $prioridad, PDO $conexion): bool
	{
		if (empty(trim($titulo))) {
			throw new Exception("El título no puede estar vacío.");
		}

		if (strlen(trim($titulo)) < 2) {
			throw new Exception("El título debe tener al menos 2 caracteres.");
		}

		try {
			// Consulta para actualizar la estrategia
			$query = <<<SQL
            UPDATE estrategias SET
                titulo = :titulo,
                texto = :texto,
                prioridad = :prioridad
            WHERE
                id = :id
                AND estado = 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':titulo', trim($titulo), PDO::PARAM_STR);
			$statement->bindValue(':texto', $texto, PDO::PARAM_STR);
			$statement->bindValue(':prioridad', $prioridad, PDO::PARAM_INT);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			$result = $statement->execute();

			// Verificar si se actualizó alguna fila
			if ($result && $statement->rowCount() === 0) {
				throw new Exception("No se encontró la estrategia o no se realizaron cambios.");
			}

			return $result;

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al modificar estrategia (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Desactiva una estrategia estableciendo su estado a 0.
	 *
	 * @param int $id       ID de la estrategia a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado a 0 (inactivo)
			$query = <<<SQL
            UPDATE estrategias SET
            	estado = 0
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			// Podríamos chequear rowCount() > 0 si queremos asegurarnos que una fila fue afectada.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			// Considera loggear el error aquí
			throw new Exception("Error de base de datos al desactivar estrategia (ID: $id): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getTitulo(): ?string
	{
		return $this->titulo;
	}

	public function setTitulo(?string $titulo): self
	{
		$this->titulo = $titulo;
		return $this;
	}

	public function getTexto(): ?string
	{
		return $this->texto;
	}

	public function setTexto(?string $texto): self
	{
		$this->texto = $texto;
		return $this;
	}

	public function getPrioridad(): ?int
	{
		return $this->prioridad;
	}

	public function setPrioridad(?int $prioridad): self
	{
		$this->prioridad = $prioridad;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	// --- Métodos adicionales (Ejemplos) ---

	/**
	 * Verifica si la estrategia está activa.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		// Asegúrate de que el estado no sea null antes de comparar
		return $this->estado === 1;
	}

	/**
	 * Valida las reglas de negocio para la creación de estrategias.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @throws Exception Si las validaciones fallan.
	 */
	private function validarReglasNegocio(PDO $conexion): void
	{
		// Validar longitud mínima del título
		if (strlen(trim($this->getTitulo())) < 2) {
			throw new Exception("El título debe tener al menos 2 caracteres.");
		}

		// Validar que la prioridad sea un número positivo si se proporciona
		if ($this->getPrioridad() !== null && $this->getPrioridad() < 0) {
			throw new Exception("La prioridad debe ser un número positivo.");
		}
	}

}
