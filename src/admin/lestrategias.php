<?php

declare(strict_types=1);

// Iniciar sesión siempre al principio
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

use App\classes\Estrategia;
use App\classes\Usuario;

// Include necessary files
require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

require_once __ROOT__ . '/vendor/autoload.php';

// Check for valid database connection
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lestrategias.php.");
	http_response_code(503);
	echo json_encode(['success' => false, 'message' => 'Error crítico: No se pudo conectar a la base de datos.']);
	exit;
}

// Initialize response array for AJAX requests
$response = [
	'success' => false,
	'message' => 'Acción no procesada.'
];

// Handle AJAX POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
	// Set content type for JSON responses
	header('Content-Type: application/json');
	
	try {
		$action = limpiar_datos($_POST['action'] ?? '');
		
		switch ($action) {
			case 'crear':
				handleCrearEstrategia($conexion, $response);
				break;
			
			case 'editar':
				handleEditarEstrategia($conexion, $response);
				break;
			
			case 'eliminar':
				handleEliminarEstrategia($conexion, $response);
				break;
			
			case 'obtener':
				handleObtenerEstrategia($conexion, $response);
				break;
			
			default:
				$response['message'] = 'Acción no válida.';
				break;
		}
		
	} catch (Exception $e) {
		error_log("Error en lestrategias.php: " . $e->getMessage());
		$response['message'] = 'Error interno del servidor. Por favor, intente de nuevo.';
	}
	
	// Return JSON response for AJAX requests
	echo json_encode($response);
	exit;
}

// For GET requests, load the estrategias list and display the view
try {
	$estrategias = Estrategia::get_list($conexion);
} catch (Exception $e) {
	error_log("Error al cargar estrategias: " . $e->getMessage());
	$estrategias = [];
}

/**
 * Handle creating a new estrategia
 */
function handleCrearEstrategia(PDO $conexion, array &$response): void
{
	try {
		// Validate and clean input data
		$titulo = limpiar_datos($_POST['titulo'] ?? '');
		$texto = limpiar_datos($_POST['texto'] ?? '');
		$prioridad = isset($_POST['prioridad']) ? (int)$_POST['prioridad'] : null;
		
		// Server-side validation
		serverSideValidation($titulo, $texto, $prioridad);
		
		// Create new estrategia object
		$estrategia = new Estrategia();
		$estrategia->setTitulo($titulo)
			->setTexto($texto)
			->setPrioridad($prioridad);
		
		// Save to database
		$nuevoId = $estrategia->crear($conexion);
		
		if ($nuevoId) {
			$response['success'] = true;
			$response['message'] = 'Estrategia creada exitosamente.';
			$response['id'] = $nuevoId;
		} else {
			throw new Exception('No se pudo crear la estrategia.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Handle editing an existing estrategia
 */
function handleEditarEstrategia(PDO $conexion, array &$response): void
{
	try {
		// Validate and clean input data
		$id = isset($_POST['id']) ? (int)$_POST['id'] : null;
		$titulo = limpiar_datos($_POST['titulo'] ?? '');
		$texto = limpiar_datos($_POST['texto'] ?? '');
		$prioridad = isset($_POST['prioridad']) ? (int)$_POST['prioridad'] : null;
		
		if (!$id || $id <= 0) {
			throw new Exception('ID de estrategia no válido.');
		}
		
		// Get current estrategia to verify it exists
		$currentEstrategia = Estrategia::get($id, $conexion);
		if (!$currentEstrategia) {
			throw new Exception('Estrategia no encontrada.');
		}
		
		// Server-side validation
		serverSideValidation($titulo, $texto, $prioridad, $id);

		// Update estrategia
		$success = Estrategia::modificar($id, $titulo, $texto, $prioridad, $conexion);
		
		if ($success) {
			$response['success'] = true;
			$response['message'] = 'Estrategia actualizada exitosamente.';
		} else {
			throw new Exception('No se pudo actualizar la estrategia.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Handle deleting a estrategia (soft delete)
 */
function handleEliminarEstrategia(PDO $conexion, array &$response): void
{
	try {
		$id = isset($_POST['id']) ? (int)$_POST['id'] : null;
		
		if (!$id || $id <= 0) {
			throw new Exception('ID de estrategia no válido.');
		}
		
		// Perform soft delete
		$success = Estrategia::desactivar($id, $conexion);
		
		if ($success) {
			$response['success'] = true;
			$response['message'] = 'Estrategia eliminada exitosamente.';
		} else {
			throw new Exception('No se pudo eliminar la estrategia.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Handle getting a estrategia for editing
 */
function handleObtenerEstrategia(PDO $conexion, array &$response): void
{
	try {
		$id = isset($_POST['id']) ? (int)$_POST['id'] : null;
		
		if (!$id || $id <= 0) {
			throw new Exception('ID de estrategia no válido.');
		}
		
		$estrategia = Estrategia::get($id, $conexion);
		
		if ($estrategia) {
			$response['success'] = true;
			$response['estrategia'] = [
				'id' => $estrategia->getId(),
				'titulo' => $estrategia->getTitulo(),
				'texto' => $estrategia->getTexto(),
				'prioridad' => $estrategia->getPrioridad()
			];
		} else {
			throw new Exception('Estrategia no encontrada.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Server-side validation
 */
function serverSideValidation(string $titulo, string $texto, ?int $prioridad, ?int $excludeId = null): void
{
	// Validate title
	if (empty(trim($titulo))) {
		throw new Exception('El título es requerido.');
	}
	
	if (strlen(trim($titulo)) < 2) {
		throw new Exception('El título debe tener al menos 2 caracteres.');
	}
	
	if (strlen(trim($titulo)) > 100) {
		throw new Exception('El título no puede exceder 100 caracteres.');
	}
	
	// Validate description
	if (empty(trim($texto))) {
		throw new Exception('La descripción es requerida.');
	}
	
	if (strlen(trim($texto)) < 10) {
		throw new Exception('La descripción debe tener al menos 10 caracteres.');
	}
	
	if (strlen(trim($texto)) > 1000) {
		throw new Exception('La descripción no puede exceder 1000 caracteres.');
	}
	
	// Validate priority
	if ($prioridad === null || $prioridad < 1) {
		throw new Exception('La prioridad es requerida y debe ser mayor a 0.');
	}
	
	if ($prioridad > 999) {
		throw new Exception('La prioridad no puede ser mayor a 999.');
	}
}

require_once __ROOT__ . '/views/admin/lestrategias.view.php';

?>
