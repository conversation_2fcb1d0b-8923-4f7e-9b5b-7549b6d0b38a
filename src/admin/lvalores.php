<?php

declare(strict_types=1);

// Iniciar sesión siempre al principio
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

use App\classes\Valor;
use App\classes\Usuario;

// Include necessary files
require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

require_once __ROOT__ . '/vendor/autoload.php';

// Check for valid database connection
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lvalores.php.");
	http_response_code(503);
	echo json_encode(['success' => false, 'message' => 'Error crítico: No se pudo conectar a la base de datos.']);
	exit;
}

// Initialize response array for AJAX requests
$response = [
	'success' => false,
	'message' => 'Acción no procesada.'
];

// Handle AJAX POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
	// Set content type for JSON responses
	header('Content-Type: application/json');
	
	try {
		$action = limpiar_datos($_POST['action'] ?? '');
		
		switch ($action) {
			case 'crear':
				handleCrearValor($conexion, $response);
				break;
			
			case 'editar':
				handleEditarValor($conexion, $response);
				break;
			
			case 'eliminar':
				handleEliminarValor($conexion, $response);
				break;
			
			case 'obtener':
				handleObtenerValor($conexion, $response);
				break;
			
			default:
				$response['message'] = 'Acción no válida.';
				break;
		}
		
	} catch (Exception $e) {
		error_log("Error en lvalores.php: " . $e->getMessage());
		$response['message'] = 'Error interno del servidor. Por favor, intente de nuevo.';
	}
	
	// Return JSON response for AJAX requests
	echo json_encode($response);
	exit;
}

// For GET requests, load the valores list and display the view
try {
	$valores = Valor::get_list($conexion);
} catch (Exception $e) {
	error_log("Error al cargar valores: " . $e->getMessage());
	$valores = [];
}

/**
 * Handle creating a new valor
 */
function handleCrearValor(PDO $conexion, array &$response): void
{
	try {
		// Validate and clean input data
		$titulo = limpiar_datos($_POST['titulo'] ?? '');
		$descripcion = limpiar_datos($_POST['descripcion'] ?? '');
		$prioridad = isset($_POST['prioridad']) ? (int)$_POST['prioridad'] : null;
		
		// Handle image upload
		$imagen = null;
		if (isset($_FILES['imagen']) && $_FILES['imagen']['error'] === UPLOAD_ERR_OK) {
			$imagen = handleImageUpload($_FILES['imagen']);
		}
		
		// Server-side validation
		serverSideValidation($titulo, $descripcion, $prioridad, $imagen);
		
		// Create new valor object
		$valor = new Valor();
		$valor->setTitulo($titulo)
			->setTexto($descripcion)
			->setPrioridad($prioridad)
			->setImagen($imagen);
		
		// Save to database
		$nuevoId = $valor->crear($conexion);
		
		if ($nuevoId) {
			$response['success'] = true;
			$response['message'] = 'Valor creado exitosamente.';
			$response['id'] = $nuevoId;
		} else {
			throw new Exception('No se pudo crear el valor.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Handle editing an existing valor
 */
function handleEditarValor(PDO $conexion, array &$response): void
{
	try {
		// Validate and clean input data
		$id = isset($_POST['id']) ? (int)$_POST['id'] : null;
		$titulo = limpiar_datos($_POST['titulo'] ?? '');
		$descripcion = limpiar_datos($_POST['descripcion'] ?? '');
		$prioridad = isset($_POST['prioridad']) ? (int)$_POST['prioridad'] : null;
		
		if (!$id || $id <= 0) {
			throw new Exception('ID de valor no válido.');
		}
		
		// Get current valor to preserve existing image if no new one is uploaded
		$currentValor = Valor::get($id, $conexion);
		if (!$currentValor) {
			throw new Exception('Valor no encontrado.');
		}
		
		// Handle image upload (optional for edit)
		$imagen = $currentValor->getImagen(); // Keep current image by default
		if (isset($_FILES['imagen']) && $_FILES['imagen']['error'] === UPLOAD_ERR_OK) {
			$imagen = handleImageUpload($_FILES['imagen']);
			// TODO: Delete old image file if needed
		}
		
		// Server-side validation
		serverSideValidation($titulo, $descripcion, $prioridad, null, $id); // Don't require image for edit

		// Update valor
		$success = Valor::modificar($id, $imagen, $titulo, $descripcion, $prioridad, $conexion);
		
		if ($success) {
			$response['success'] = true;
			$response['message'] = 'Valor actualizado exitosamente.';
		} else {
			throw new Exception('No se pudo actualizar el valor.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Handle deleting a valor (soft delete)
 */
function handleEliminarValor(PDO $conexion, array &$response): void
{
	try {
		$id = isset($_POST['id']) ? (int)$_POST['id'] : null;
		
		if (!$id || $id <= 0) {
			throw new Exception('ID de valor no válido.');
		}
		
		// Perform soft delete
		$success = Valor::desactivar($id, $conexion);
		
		if ($success) {
			$response['success'] = true;
			$response['message'] = 'Valor eliminado exitosamente.';
		} else {
			throw new Exception('No se pudo eliminar el valor.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Handle getting a valor for editing
 */
function handleObtenerValor(PDO $conexion, array &$response): void
{
	try {
		$id = isset($_POST['id']) ? (int)$_POST['id'] : null;
		
		if (!$id || $id <= 0) {
			throw new Exception('ID de valor no válido.');
		}
		
		$valor = Valor::get($id, $conexion);
		
		if ($valor) {
			$response['success'] = true;
			$response['valor'] = [
				'id' => $valor->getId(),
				'titulo' => $valor->getTitulo(),
				'descripcion' => $valor->getTexto(),
				'prioridad' => $valor->getPrioridad(),
				'imagen' => $valor->getImagen()
			];
		} else {
			throw new Exception('Valor no encontrado.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Handle image upload for valores
 */
function handleImageUpload(array $file): string
{
	// Validate file
	if ($file['error'] !== UPLOAD_ERR_OK) {
		throw new Exception('Error al subir la imagen.');
	}
	
	// Check file size (8MB limit)
	$maxSize = 8 * 1024 * 1024; // 8MB in bytes
	if ($file['size'] > $maxSize) {
		throw new Exception('La imagen es demasiado grande. Máximo 8MB permitido.');
	}
	
	// Check file type
	$allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
	$finfo = finfo_open(FILEINFO_MIME_TYPE);
	$mimeType = finfo_file($finfo, $file['tmp_name']);
	finfo_close($finfo);
	
	if (!in_array($mimeType, $allowedTypes)) {
		throw new Exception('Tipo de archivo no permitido. Solo se permiten imágenes JPG, PNG y WebP.');
	}
	
	// Create upload directory if it doesn't exist
	$uploadDir = __ROOT__ . '/resources/images/valores/';
	if (!is_dir($uploadDir)) {
		if (!mkdir($uploadDir, 0755, true)) {
			throw new Exception('No se pudo crear el directorio de imágenes.');
		}
	}
	
	// Generate unique filename
	$extension = pathinfo($file['name'], PATHINFO_EXTENSION);
	$filename = 'valor_' . time() . '_' . uniqid() . '.' . $extension;
	$targetPath = $uploadDir . $filename;
	
	// Move uploaded file
	if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
		throw new Exception('Error al guardar la imagen.');
	}
	
	return $filename;
}

/**
 * Server-side validation
 */
function serverSideValidation(string $titulo, string $descripcion, ?int $prioridad, ?string $imagen, ?int $excludeId = null): void
{
	// Validate title
	if (empty(trim($titulo))) {
		throw new Exception('El título es requerido.');
	}
	
	if (strlen(trim($titulo)) < 2) {
		throw new Exception('El título debe tener al menos 2 caracteres.');
	}
	
	if (strlen(trim($titulo)) > 100) {
		throw new Exception('El título no puede exceder 100 caracteres.');
	}
	
	// Validate description
	if (empty(trim($descripcion))) {
		throw new Exception('La descripción es requerida.');
	}
	
	if (strlen(trim($descripcion)) < 10) {
		throw new Exception('La descripción debe tener al menos 10 caracteres.');
	}
	
	if (strlen(trim($descripcion)) > 1000) {
		throw new Exception('La descripción no puede exceder 1000 caracteres.');
	}
	
	// Validate priority
	if ($prioridad === null || $prioridad < 1) {
		throw new Exception('La prioridad es requerida y debe ser mayor a 0.');
	}
	
	if ($prioridad > 999) {
		throw new Exception('La prioridad no puede ser mayor a 999.');
	}
	
	// Validate image is required for creation
	if ($excludeId === null && empty($imagen)) {
		throw new Exception('La imagen es requerida.');
	}
}

require_once __ROOT__ . '/views/admin/lvalores.view.php';

?>
