<?php

// Iniciar sesión siempre al principio
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

use App\classes\Partner;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

require_once __ROOT__ . '/vendor/autoload.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lpartners.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

// Initialize response array for AJAX requests
$response = [
	'success' => false,
	'message' => 'Acción no completada.'
];

// Initialize variables for view
$partners = [];
$success_text = '';
$success_display = 'hide';
$error_text = '';
$error_display = 'hide';

// Handle AJAX POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
	// Set content type for JSON responses
	header('Content-Type: application/json');
	
	try {
		$action = limpiar_datos($_POST['action'] ?? '');
		
		switch ($action) {
			case 'crear':
				handleCrearPartner($conexion, $response);
				break;
			
			case 'editar':
				handleEditarPartner($conexion, $response);
				break;
			
			case 'eliminar':
				handleEliminarPartner($conexion, $response);
				break;
			
			case 'obtener':
				handleObtenerPartner($conexion, $response);
				break;
			
			default:
				$response['message'] = 'Acción no válida.';
				break;
		}
		
	} catch (Exception $e) {
		error_log("Error en lpartners.php: " . $e->getMessage());
		$response['message'] = 'Error interno del servidor. Por favor, intente de nuevo.';
	}
	
	// Return JSON response for AJAX requests
	echo json_encode($response);
	exit;
}

// Handle GET requests - Load data for the view
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
	try {
		// Load all partners ordered by priority
		$partners = Partner::get_list($conexion);
		
	} catch (Exception $e) {
		error_log("Error loading partners: " . $e->getMessage());
		$error_text = 'Error al cargar los partners: ' . $e->getMessage();
		$error_display = 'show';
	}
}

/**
 * Handle creating a new partner
 */
function handleCrearPartner(PDO $conexion, array &$response): void
{
	try {
		// Validate and clean input data
		$prioridad = isset($_POST['prioridad']) ? (int)$_POST['prioridad'] : null;
		
		// Handle image upload
		$imagen = null;
		if (isset($_FILES['imagen']) && $_FILES['imagen']['error'] === UPLOAD_ERR_OK) {
			$imagen = handleImageUpload($_FILES['imagen']);
		}
		
		// Server-side validation
		serverSideValidation($prioridad, $imagen);
		
		// Create new partner object
		$partner = new Partner();
		$partner->setPrioridad($prioridad)
			->setImagen($imagen);
		
		// Save to database
		$newId = $partner->crear($conexion);
		
		if ($newId) {
			$response['success'] = true;
			$response['message'] = 'Partner creado exitosamente.';
			$response['partner_id'] = $newId;
		} else {
			throw new Exception('No se pudo crear el partner.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Handle editing an existing partner
 */
function handleEditarPartner(PDO $conexion, array &$response): void
{
	try {
		// Validate and clean input data
		$id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
		$prioridad = isset($_POST['prioridad']) ? (int)$_POST['prioridad'] : null;
		
		if ($id <= 0) {
			throw new Exception('ID de partner no válido.');
		}
		
		// Get current partner data
		$currentPartner = Partner::get($id, $conexion);
		if (!$currentPartner) {
			throw new Exception('Partner no encontrado.');
		}
		
		// Handle image upload (optional for edit)
		$imagen = $currentPartner->getImagen(); // Keep current image by default
		if (isset($_FILES['imagen']) && $_FILES['imagen']['error'] === UPLOAD_ERR_OK) {
			$imagen = handleImageUpload($_FILES['imagen']);
			// TODO: Delete old image file if needed
		}
		
		// Server-side validation
		serverSideValidation($prioridad, null, $id); // Don't require image for edit

		// Validate unique priority for editing
		Partner::validarPrioridadUnica($prioridad, $id, $conexion);
		
		// Update partner
		$success = Partner::modificar($id, $prioridad, $imagen, $conexion);
		
		if ($success) {
			$response['success'] = true;
			$response['message'] = 'Partner actualizado exitosamente.';
		} else {
			throw new Exception('No se pudo actualizar el partner.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Handle deleting a partner
 */
function handleEliminarPartner(PDO $conexion, array &$response): void
{
	try {
		$id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
		
		if ($id <= 0) {
			throw new Exception('ID de partner no válido.');
		}
		
		// Soft delete (deactivate)
		$success = Partner::desactivar($id, $conexion);
		
		if ($success) {
			$response['success'] = true;
			$response['message'] = 'Partner eliminado exitosamente.';
		} else {
			throw new Exception('No se pudo eliminar el partner.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Handle getting partner data for editing
 */
function handleObtenerPartner(PDO $conexion, array &$response): void
{
	try {
		$id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
		
		if ($id <= 0) {
			throw new Exception('ID de partner no válido.');
		}
		
		$partner = Partner::get($id, $conexion);
		
		if ($partner) {
			$response['success'] = true;
			$response['partner'] = [
				'id' => $partner->getId(),
				'prioridad' => $partner->getPrioridad(),
				'imagen' => $partner->getImagen()
			];
		} else {
			throw new Exception('Partner no encontrado.');
		}
		
	} catch (Exception $e) {
		$response['message'] = $e->getMessage();
	}
}

/**
 * Handle image upload with validation
 */
function handleImageUpload(array $file): string
{
	$uploadDir = __ROOT__ . '/resources/images/partners/';
	$allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
	$maxFileSize = 600 * 1024; // 600KB
	
	// Validate file type
	if (!in_array($file['type'], $allowedTypes)) {
		throw new Exception('Tipo de archivo no permitido. Solo se permiten imágenes (JPG, PNG, GIF, WebP).');
	}
	
	// Validate file size
	if ($file['size'] > $maxFileSize) {
		throw new Exception('El archivo es demasiado grande. Tamaño máximo: 600KB.');
	}
	
	// Generate unique filename
	$extension = pathinfo($file['name'], PATHINFO_EXTENSION);
	$filename = 'partner_' . time() . '_' . uniqid() . '.' . $extension;
	$targetPath = $uploadDir . $filename;
	
	// Create directory if it doesn't exist
	if (!is_dir($uploadDir)) {
		mkdir($uploadDir, 0755, true);
	}
	
	// Move uploaded file
	if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
		throw new Exception('Error al subir el archivo.');
	}
	
	return $filename;
}

/**
 * Server-side validation
 */
function serverSideValidation(?int $prioridad, ?string $imagen, ?int $excludeId = null): void
{
	// Validate priority
	if ($prioridad === null || $prioridad < 1) {
		throw new Exception('La prioridad es requerida y debe ser mayor a 0.');
	}
	
	if ($prioridad > 999) {
		throw new Exception('La prioridad no puede ser mayor a 999.');
	}
	
	// Validate image is required for creation
	if ($excludeId === null && empty($imagen)) {
		throw new Exception('La imagen es requerida.');
	}
}

require_once __ROOT__ . '/views/admin/lpartners.view.php';

?>
