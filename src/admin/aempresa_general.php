<?php

// Iniciar sesión siempre al principio
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

use App\classes\ConfigWeb;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

require_once __ROOT__ . '/vendor/autoload.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en aempresa_general.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

// Initialize variables for form and feedback
$configData = null;
$success_text = '';
$success_display = 'hide';
$error_text = '';
$error_display = 'hide';

#region Handle Flash Messages
// Check for success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text = $_SESSION['flash_message_success'];
	$success_display = 'show';
	unset($_SESSION['flash_message_success']); // Clear after preparing display
}

// Check for error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text = $_SESSION['flash_message_error'];
	$error_display = 'show';
	unset($_SESSION['flash_message_error']); // Clear after preparing display
}
#endregion Handle Flash Messages

#region Load Existing Configuration Data
try {
	// Load existing configuration data to pre-populate the form
	$configData = ConfigWeb::getPrincipal($conexion);
} catch (Exception $e) {
	error_log("Error loading configuration data: " . $e->getMessage());
	// Continue without pre-populated data - form will be empty
}
#endregion Load Existing Configuration Data

#region POST Request Handling
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	// Reset feedback vars for this request attempt
	$success_text = '';
	$success_display = 'hide';
	$error_text = '';
	$error_display = 'hide';

	try {
		// 1. Get data from $_POST and clean it
		$quienes_somos = !empty($_POST['quienes_somos']) ? limpiar_datos($_POST['quienes_somos']) : null;
		$mision = !empty($_POST['mision']) ? limpiar_datos($_POST['mision']) : null;
		$vision = !empty($_POST['vision']) ? limpiar_datos($_POST['vision']) : null;

		// 2. Basic validation - at least one field should have content
		if (empty($quienes_somos) && empty($mision) && empty($vision)) {
			throw new Exception("Debe completar al menos uno de los campos de contenido institucional.");
		}

		// 3. Check if configuration exists and update or create accordingly
		$existingConfig = ConfigWeb::getPrincipal($conexion);

		if ($existingConfig) {
			// Update existing configuration using the specific method for institutional content
			$success = ConfigWeb::actualizarContenidoInstitucional(
				$existingConfig->getId(),
				$quienes_somos,
				$mision,
				$vision,
				$conexion
			);

			if ($success) {
				$_SESSION['flash_message_success'] = "Información general de la empresa actualizada correctamente.";
				header('Location: nuestra-empresa-general');
				exit;
			} else {
				throw new Exception("No se pudo actualizar la información general de la empresa.");
			}
		} else {
			// Create new configuration if none exists
			$newConfig = new ConfigWeb();
			$newConfig->setQuienesSomos($quienes_somos)
					  ->setMision($mision)
					  ->setVision($vision);

			$newId = $newConfig->crear($conexion);

			if ($newId) {
				$_SESSION['flash_message_success'] = "Información general de la empresa creada correctamente.";
				header('Location: nuestra-empresa-general');
				exit;
			} else {
				throw new Exception("No se pudo crear la información general de la empresa.");
			}
		}

	} catch (PDOException $e) {
		// Handle database errors
		error_log("Database error in company general information: " . $e->getMessage());
		$error_text = 'Error de base de datos al guardar la información. Por favor, intente de nuevo más tarde.';
		$error_display = 'show';
	} catch (Exception $e) {
		// Handle validation errors or other errors
		error_log("Error in company general information: " . $e->getMessage());
		$error_text = 'Error: ' . htmlspecialchars($e->getMessage());
		$error_display = 'show';
	}
}
#endregion POST Request Handling

require_once __ROOT__ . '/views/admin/aempresa_general.view.php';

?>
