<?php

// Iniciar sesión siempre al principio
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

use App\classes\ConfigWeb;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

require_once __ROOT__ . '/vendor/autoload.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en ainicio_banner.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

// Initialize variables for form and feedback
$configData = null;
$success_text = '';
$success_display = 'hide';
$error_text = '';
$error_display = 'hide';

#region Handle Flash Messages
// Check for success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text = $_SESSION['flash_message_success'];
	$success_display = 'show';
	unset($_SESSION['flash_message_success']); // Clear after preparing display
}

// Check for error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text = $_SESSION['flash_message_error'];
	$error_display = 'show';
	unset($_SESSION['flash_message_error']); // Clear after preparing display
}
#endregion Handle Flash Messages

#region Load Existing Configuration Data
try {
	// Load existing configuration data to pre-populate the form
	$configData = ConfigWeb::getPrincipal($conexion);
} catch (Exception $e) {
	error_log("Error loading configuration data: " . $e->getMessage());
	// Continue without pre-populated data - form will be empty
}
#endregion Load Existing Configuration Data

#region POST Request Handling
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	// Reset feedback vars for this request attempt
	$success_text = '';
	$success_display = 'hide';
	$error_text = '';
	$error_display = 'hide';

	try {
		// 1. Validate and sanitize input data
		$titulo_banner_inicio = limpiar_datos($_POST['titulo_banner_inicio'] ?? '');
		$texto_banner_inicio = limpiar_datos($_POST['texto_banner_inicio'] ?? '');
		
		// Keep existing video if no new file uploaded
		$video_banner_inicio = $configData ? $configData->getVideoBannerInicio() : null;

		// 2. Handle file upload if provided
		if (isset($_FILES['video_banner_inicio']) && $_FILES['video_banner_inicio']['error'] !== UPLOAD_ERR_NO_FILE) {
			$uploadResult = handleVideoUpload($_FILES['video_banner_inicio']);
			if ($uploadResult['success']) {
				$video_banner_inicio = $uploadResult['filename'];
			} else {
				throw new Exception($uploadResult['message']);
			}
		}

		// 3. Check if configuration exists and update or create accordingly
		$existingConfig = ConfigWeb::getPrincipal($conexion);

		if ($existingConfig) {
			// Update existing configuration using the specific banner method
			$success = ConfigWeb::actualizarBannerInicio(
				$existingConfig->getId(),
				$video_banner_inicio,
				$titulo_banner_inicio,
				$texto_banner_inicio,
				$conexion
			);

			if ($success) {
				$_SESSION['flash_message_success'] = "Banner de inicio actualizado correctamente.";
				header('Location: inicio-banner');
				exit;
			} else {
				throw new Exception("No se pudo actualizar el banner de inicio.");
			}
		} else {
			// Create new configuration with banner data
			$newConfig = new ConfigWeb();
			$newConfig->setVideoBannerInicio($video_banner_inicio)
					  ->setTituloBannerInicio($titulo_banner_inicio)
					  ->setTextoBannerInicio($texto_banner_inicio);

			$newId = $newConfig->crear($conexion);

			if ($newId) {
				$_SESSION['flash_message_success'] = "Banner de inicio creado correctamente.";
				header('Location: inicio-banner');
				exit;
			} else {
				throw new Exception("No se pudo crear la configuración del banner.");
			}
		}

	} catch (PDOException $e) {
		// Handle database errors
		error_log("Database error in banner configuration: " . $e->getMessage());
		$error_text = 'Error de base de datos al guardar el banner. Por favor, intente de nuevo más tarde.';
		$error_display = 'show';
	} catch (Exception $e) {
		// Handle validation errors or other errors
		error_log("Error in banner configuration: " . $e->getMessage());
		$error_text = 'Error: ' . htmlspecialchars($e->getMessage());
		$error_display = 'show';
	}
}
#endregion POST Request Handling

#region File Upload Handler Function
/**
 * Handles video file upload with validation
 * @param array $file $_FILES array element
 * @return array Result with success status and message/filename
 */
function handleVideoUpload($file): array {
	// Check for upload errors
	if ($file['error'] !== UPLOAD_ERR_OK) {
		return ['success' => false, 'message' => 'Error al subir el archivo: ' . getUploadErrorMessage($file['error'])];
	}

	// Validate file size (8MB max)
	$maxSize = 15 * 1024 * 1024; // 8MB in bytes
	if ($file['size'] > $maxSize) {
		return ['success' => false, 'message' => 'El archivo es demasiado grande. Tamaño máximo permitido: 8MB.'];
	}

	// Validate file type
	$allowedTypes = ['video/webm', 'video/mp4', 'video/avi', 'video/mov', 'video/wmv'];
	$finfo = finfo_open(FILEINFO_MIME_TYPE);
	$mimeType = finfo_file($finfo, $file['tmp_name']);
	finfo_close($finfo);

	if (!in_array($mimeType, $allowedTypes)) {
		return ['success' => false, 'message' => 'Tipo de archivo no permitido. Use formatos: .webm, .mp4, .avi, .mov, .wmv'];
	}

	// Generate unique filename
	$extension = pathinfo($file['name'], PATHINFO_EXTENSION);
	$filename = 'banner_' . time() . '_' . uniqid() . '.' . $extension;
	$uploadPath = __ROOT__ . '/resources/videos/' . $filename;

	// Create directory if it doesn't exist
	$uploadDir = dirname($uploadPath);
	if (!is_dir($uploadDir)) {
		mkdir($uploadDir, 0755, true);
	}

	// Move uploaded file
	if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
		return ['success' => true, 'filename' => $filename];
	} else {
		return ['success' => false, 'message' => 'Error al guardar el archivo en el servidor.'];
	}
}

/**
 * Get human-readable upload error message
 * @param int $errorCode PHP upload error code
 * @return string Error message
 */
function getUploadErrorMessage($errorCode): string {
	switch ($errorCode) {
		case UPLOAD_ERR_INI_SIZE:
		case UPLOAD_ERR_FORM_SIZE:
			return 'El archivo es demasiado grande.';
		case UPLOAD_ERR_PARTIAL:
			return 'El archivo se subió parcialmente.';
		case UPLOAD_ERR_NO_TMP_DIR:
			return 'Falta la carpeta temporal.';
		case UPLOAD_ERR_CANT_WRITE:
			return 'Error al escribir el archivo en el disco.';
		case UPLOAD_ERR_EXTENSION:
			return 'Subida de archivo detenida por extensión.';
		default:
			return 'Error desconocido.';
	}
}
#endregion File Upload Handler Function

require_once __ROOT__ . '/views/admin/ainicio_banner.view.php';

?>
