<?php
// global $conexion, $lastmodule; // Declaration moved down

// BEGIN redirect SSL
// funcion para obligar todo el trafico de la pagina a traves del canal SSL.
$host = $_SERVER['HTTP_HOST'];
$uri = $_SERVER['REQUEST_URI'];

// Skip redirect if running locally
if ($host !== 'localhost' && $host !== '127.0.0.1') {
	$isHttp = empty($_SERVER['HTTPS']) || $_SERVER['HTTPS'] === "off";
	$hasWWW = stripos($host, 'www.') === 0;
	
	// Remove "www." if present
	if ($hasWWW) {
		$host = preg_replace('/^www\./i', '', $host);
	}
	
	// Redirect if not HTTPS or has "www."
	if ($isHttp || $hasWWW) {
		$location = 'https://' . $host . $uri;
		header('HTTP/1.1 301 Moved Permanently');
		header('Location: ' . $location);
		exit();
	}
}
// END redirect SSL

use \App\classes\ConfigWeb;

require_once __ROOT__ . '/src/query/connection.php';
require_once __ROOT__ . '/vendor/autoload.php';

global $conexion;

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en form_aliados_comerciales.php (POST).");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

try {
	$error_text      = '';
	$error_display   = 'hide';
	$success_text 	 = '';
	$success_display = 'hide';
	
	$configuraciones_web  = ConfigWeb::getPrincipal($conexion);
	
} catch (Exception $e) {
	$error_display = 'show';
	$error_text = $e->getMessage();
}


?>
