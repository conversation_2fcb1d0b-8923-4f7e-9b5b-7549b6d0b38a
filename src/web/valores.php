<?php

use App\classes\Valor;

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar_web.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en valores.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

try {
	// Load active valores ordered by priority
	$valores = Valor::get_list($conexion);
} catch (Exception $e) {
	error_log("Error al cargar valores: " . $e->getMessage());
	$valores = [];
}

require_once __ROOT__ . '/views/web/valores.view.php';

?>