<?php

session_start();

use App\classes\ConfigWeb;

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar_web.php';

#region region init variables
$quienes_somos='';
#endregion init variables
#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion get
#region try
try {
	$configuraciones_web = ConfigWeb::getPrincipal($conexion);
	$quienes_somos       = $configuraciones_web->getQuienesSomos();

} catch (Exception $e) {
	$error_display = 'show';
	$error_text    = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/web/quienes_somos.view.php';

?>