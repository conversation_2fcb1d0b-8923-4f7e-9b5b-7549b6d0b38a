<?php session_start();

/** @var PDO $conexion */
global $conexion;

use App\classes\Estrategia;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar_web.php';
require_once __ROOT__ . '/vendor/autoload.php';

#region region init variables
$estrategias = [];
#endregion init variables

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
		// Load strategies from database
		$estrategias = Estrategia::get_list($conexion);
	} catch (Exception $e) {
		error_log("Error al cargar estrategias: " . $e->getMessage());
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion get

#region try
try {
	// Additional processing if needed
} catch (Exception $e) {
	$error_display = 'show';
	$error_text    = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/web/estrategias.view.php';

?>