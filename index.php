<?php

use App\classes\ServicioCategoria;
use App\classes\Servicio;
use App\classes\Partner;

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 1) . '/config/config.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar_web.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en form_aliados_comerciales.php (POST).");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region region init variables
$categorias_servicios    = [];
$servicios_por_categoria = [];
$partners               = [];
#endregion init variables

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
		// Obtener todas las categorías de servicios activas ordenadas por prioridad
		$categorias_servicios = ServicioCategoria::get_list($conexion);
		$servicios_completo   = Servicio::get_list($conexion);

		// Para cada categoría, obtener sus servicios
		foreach ($categorias_servicios as $categoria) {
			$id_categoria = $categoria->getId();
			$categoria->setServicios(Servicio::get_list_by_categoria($id_categoria, $conexion));
		}

		// Obtener todos los partners ordenados por prioridad
		$partners = Partner::get_list($conexion);

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion get

require_once __ROOT__ . '/views/web/index.view.php';

?>