# Partners CRUD Admin Module Setup

## Overview
This document provides setup instructions for the new Partners CRUD admin module that has been created for managing partners on the website.

## Files Created

### Admin Module Files
- `src/admin/lpartners.php` - Admin controller with AJAX handlers
- `views/admin/lpartners.view.php` - Admin view with DataTables and modal forms
- `resources/js/lpartners.js` - JavaScript for frontend functionality
- `resources/css/lpartners.css` - Custom CSS styling

### Configuration Changes
- `.htaccess` - Added friendly URL route: `admin/listado-partners`
- `index.php` - Updated to load partners from database
- `views/web/index.view.php` - Updated to display dynamic partners

### Database Schema Update Required

**IMPORTANT**: The partners table needs to be updated to support soft delete functionality.

#### Required SQL Migration:
```sql
-- Add estado column to partners table for soft delete functionality
ALTER TABLE partners ADD COLUMN estado INT(1) NOT NULL DEFAULT 1 COMMENT 'Estado: 1=Activo, 0=Inactivo';

-- Update existing records to be active
UPDATE partners SET estado = 1 WHERE estado IS NULL;

-- Add index for better performance
CREATE INDEX idx_partners_estado_prioridad ON partners (estado, prioridad);
```

## Features Implemented

### Admin Interface
- ✅ DataTables with responsive design
- ✅ Modal dialogs for create/edit operations
- ✅ Image upload with validation (600KB max, 800x128 recommended)
- ✅ Priority-based ordering (ascending: lowest to highest)
- ✅ SweetAlert confirmations for delete actions
- ✅ Dark UI theme consistent with existing admin pages
- ✅ Server-side and client-side validation
- ✅ Auto-refresh table after CRUD operations

### Image Upload Specifications
- **Accepted formats**: JPG, PNG, GIF, WebP
- **Recommended dimensions**: 800 x 128 pixels
- **Maximum file size**: 600KB
- **Upload directory**: `/resources/images/partners/`

### Soft Delete Implementation
- ✅ Partners are deactivated (estado = 0) instead of physically deleted
- ✅ Only active partners (estado = 1) are displayed on website
- ✅ Priority validation only considers active partners
- ✅ Follows existing patterns from Usuario.php and other classes

### Website Integration
- ✅ Partners section in homepage now loads from database
- ✅ Partners displayed in infinite scrolling carousel
- ✅ Ordered by priority (ascending)
- ✅ Graceful handling when no partners exist

## Access Information

### Admin URL
- **Friendly URL**: `https://yoursite.com/admin/listado-partners`
- **Direct URL**: `https://yoursite.com/src/admin/lpartners.php`

### Menu Location
The Partners admin module is accessible through:
- **Admin Sidebar** → **Inicio** → **Partners**

## Technical Implementation Details

### Code Style Compliance
- ✅ Follows existing PHP class patterns with proper documentation
- ✅ Maintains consistency with current admin interface styling
- ✅ Uses singular class naming conventions
- ✅ Implements proper separation of concerns
- ✅ Database operations handled within class methods

### Security Features
- ✅ CSRF protection through session validation
- ✅ File upload validation (type, size)
- ✅ SQL injection prevention with prepared statements
- ✅ Input sanitization and validation
- ✅ Proper error handling and logging

### Performance Optimizations
- ✅ Database indexes for efficient querying
- ✅ Lazy loading for partner images
- ✅ Optimized DataTables configuration
- ✅ Minimal JavaScript footprint

## Testing Recommendations

### Before Going Live
1. **Run the database migration** to add the `estado` column
2. **Test image uploads** with various file types and sizes
3. **Verify priority validation** works correctly
4. **Test soft delete functionality** 
5. **Check responsive design** on mobile devices
6. **Validate website integration** displays partners correctly

### Test Cases
- Create partner with valid image and priority
- Edit partner priority and image
- Delete partner (should soft delete)
- Upload oversized image (should fail)
- Upload invalid file type (should fail)
- Create partner with duplicate priority (should fail)
- Verify website displays partners in correct order

## Future Enhancements

### Potential Improvements
- Partner name/title field
- Partner website URL field
- Partner description field
- Bulk operations (activate/deactivate multiple)
- Image resizing/optimization
- Partner categories/grouping
- Analytics tracking for partner clicks

## Support Notes

### Common Issues
1. **Images not displaying**: Check file permissions on `/resources/images/partners/` directory
2. **Upload failures**: Verify PHP upload limits and directory permissions
3. **Priority conflicts**: Ensure database migration was run correctly
4. **Menu not showing**: Clear browser cache and check user permissions

### File Permissions
Ensure the following directories have write permissions:
- `/resources/images/partners/` (755 or 775)

### PHP Configuration
Recommended PHP settings:
- `upload_max_filesize = 2M`
- `post_max_size = 8M`
- `max_file_uploads = 20`

## Conclusion

The Partners CRUD admin module is now fully implemented and ready for use. The system follows all existing code patterns and user preferences, including soft delete functionality, dark UI theme, and proper separation of concerns.

Remember to run the database migration before using the module in production.
