/**
 * PARTNERS ADMIN MODULE JAVASCRIPT
 * Handles CRUD operations for partners management
 */

$(document).ready(function() {
    // Initialize form handlers
    initializeFormHandlers();

    // Initialize modal handlers
    initializeModalHandlers();
});

/**
 * Initialize form submission handlers
 */
function initializeFormHandlers() {
    $('#formPartner').on('submit', function(e) {
        e.preventDefault();
        
        // Clear previous validation states
        clearValidationStates();
        
        // Validate form
        if (!validateForm()) {
            return;
        }
        
        // Show loading state
        showLoadingState(true);
        
        // Prepare form data
        const formData = new FormData(this);
        
        // Submit form via AJAX
        $.ajax({
            url: 'listado-partners',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                handleFormSuccess(response);
            },
            error: function(xhr, status, error) {
                handleFormError(xhr, status, error);
            },
            complete: function() {
                showLoadingState(false);
            }
        });
    });
}

/**
 * Initialize modal event handlers
 */
function initializeModalHandlers() {
    // Reset form when modal is hidden
    $('#modalPartner').on('hidden.bs.modal', function() {
        resetForm();
    });
    
    // Handle image preview
    $('#imagen').on('change', function() {
        previewImage(this);
    });
}

/**
 * Open modal for creating new partner
 */
function abrirModalCrear() {
    resetForm();
    $('#modalPartnerLabel').text('Crear Nuevo Partner');
    $('#action').val('crear');
    $('#btnSubmit').text('Crear');
    $('#imagen').prop('required', true);
    $('#currentImagePreview').hide();
}

/**
 * Open modal for editing existing partner
 */
function abrirModalEditar(partnerId) {
    resetForm();
    $('#modalPartnerLabel').text('Editar Partner');
    $('#action').val('editar');
    $('#partnerId').val(partnerId);
    $('#btnSubmit').text('Actualizar');
    $('#imagen').prop('required', false);
    
    // Load partner data
    loadPartnerData(partnerId);
    
    // Show modal
    $('#modalPartner').modal('show');
}

/**
 * Load partner data for editing
 */
function loadPartnerData(partnerId) {
    showLoadingState(true);
    
    $.ajax({
        url: 'listado-partners',
        type: 'POST',
        data: {
            action: 'obtener',
            id: partnerId
        },
        dataType: 'json',
        success: function(response) {
            if (response.success && response.partner) {
                const partner = response.partner;
                
                // Populate form fields
                $('#prioridad').val(partner.prioridad);
                
                // Show current image if exists
                if (partner.imagen) {
                    $('#currentImage').attr('src', RUTA_RESOURCES + 'images/partners/' + partner.imagen);
                    $('#currentImagePreview').show();
                }
            } else {
                showSweetAlertError('Error', response.message || 'No se pudo cargar los datos del partner.');
            }
        },
        error: function(xhr, status, error) {
            showSweetAlertError('Error', 'Error al cargar los datos del partner.');
        },
        complete: function() {
            showLoadingState(false);
        }
    });
}

/**
 * Delete partner with confirmation
 */
function eliminarPartner(partnerId) {
    swal({
        title: '¿Está seguro?',
        text: 'Esta acción eliminará permanentemente el partner. No se puede deshacer.',
        icon: 'warning',
        buttons: {
            cancel: {
                text: 'Cancelar',
                value: null,
                visible: true,
                className: 'btn-secondary',
                closeModal: true
            },
            confirm: {
                text: 'Sí, eliminar',
                value: true,
                visible: true,
                className: 'btn-danger',
                closeModal: true
            }
        },
        dangerMode: true
    }).then((willDelete) => {
        if (willDelete) {
            performDelete(partnerId);
        }
    });
}

/**
 * Perform the actual delete operation
 */
function performDelete(partnerId) {
    $.ajax({
        url: 'listado-partners',
        type: 'POST',
        data: {
            action: 'eliminar',
            id: partnerId
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showSweetAlertSuccess('Éxito', response.message);
                // Reload page to refresh the table
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showSweetAlertError('Error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showSweetAlertError('Error', 'Error al eliminar el partner.');
        }
    });
}

/**
 * Validate form fields
 */
function validateForm() {
    let isValid = true;
    
    // Validate priority
    const prioridad = $('#prioridad').val();
    if (!prioridad || prioridad < 1 || prioridad > 999) {
        showFieldError('prioridad', 'La prioridad debe ser un número entre 1 y 999.');
        isValid = false;
    }
    
    // Validate image for creation
    const action = $('#action').val();
    const imagen = $('#imagen')[0].files[0];
    if (action === 'crear' && !imagen) {
        showFieldError('imagen', 'La imagen es requerida.');
        isValid = false;
    }
    
    // Validate image file if provided
    if (imagen) {
        const maxSize = 600 * 1024; // 600KB
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        
        if (imagen.size > maxSize) {
            showFieldError('imagen', 'El archivo es demasiado grande. Tamaño máximo: 600KB.');
            isValid = false;
        }
        
        if (!allowedTypes.includes(imagen.type)) {
            showFieldError('imagen', 'Tipo de archivo no permitido. Solo se permiten imágenes.');
            isValid = false;
        }
    }
    
    return isValid;
}

/**
 * Show field validation error
 */
function showFieldError(fieldId, message) {
    const field = $('#' + fieldId);
    field.addClass('is-invalid');
    field.siblings('.invalid-feedback').text(message);
}

/**
 * Clear all validation states
 */
function clearValidationStates() {
    $('.form-control').removeClass('is-invalid is-valid');
    $('.invalid-feedback').text('');
}

/**
 * Handle successful form submission
 */
function handleFormSuccess(response) {
    if (response.success) {
        showSweetAlertSuccess('Éxito', response.message);
        $('#modalPartner').modal('hide');
        
        // Reload page to refresh the table
        setTimeout(() => {
            location.reload();
        }, 1500);
    } else {
        showSweetAlertError('Error', response.message);
    }
}

/**
 * Handle form submission error
 */
function handleFormError(xhr, status, error) {
    let errorMessage = 'Error al procesar la solicitud.';
    
    if (xhr.responseJSON && xhr.responseJSON.message) {
        errorMessage = xhr.responseJSON.message;
    }
    
    showSweetAlertError('Error', errorMessage);
}

/**
 * Show/hide loading state
 */
function showLoadingState(show) {
    const spinner = $('#loadingSpinner');
    const submitBtn = $('#btnSubmit');
    
    if (show) {
        spinner.removeClass('d-none');
        submitBtn.prop('disabled', true);
    } else {
        spinner.addClass('d-none');
        submitBtn.prop('disabled', false);
    }
}

/**
 * Reset form to initial state
 */
function resetForm() {
    $('#formPartner')[0].reset();
    $('#partnerId').val('');
    clearValidationStates();
    $('#currentImagePreview').hide();
}

/**
 * Preview selected image
 */
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            // You could add image preview functionality here if needed
            console.log('Image selected:', input.files[0].name);
        };
        
        reader.readAsDataURL(input.files[0]);
    }
}
