/**
 * Custom Video Banner Handler
 * Clean implementation for video background without theme conflicts
 */

document.addEventListener('DOMContentLoaded', function() {
    const video = document.querySelector('.custom-video-bg');

    if (!video) {
        return;
    }

    const source = video.querySelector('source');

    // Test video file accessibility
    if (source && source.src) {
        fetch(source.src, { method: 'HEAD' })
            .then(response => {
                if (!response.ok) {
                    console.error('Video file not accessible:', response.status);
                }
            })
            .catch(error => {
                console.error('Error checking video file:', error);
            });
    }

    // Handle video loading states
    video.addEventListener('loadeddata', function() {
        video.setAttribute('data-loaded', 'true');
    });

    // Handle video loading errors
    video.addEventListener('error', function(e) {
        console.error('Custom video failed to load:', e);
        console.error('Video error details:', video.error);
        video.style.display = 'none';
        // Fallback background will be visible via CSS
    });

    // Ensure video plays
    video.addEventListener('canplay', function() {
        const playPromise = video.play();

        if (playPromise !== undefined) {
            playPromise.then(function() {
                // Video started successfully
            }).catch(function(error) {
                console.warn('Video autoplay was prevented:', error);
            });
        }
    });

    // Force play attempt after a short delay
    setTimeout(function() {
        if (video.paused) {
            video.play().catch(function(error) {
                console.warn('Forced video play failed:', error);
            });
        }
    }, 1000);
    
    // Performance optimization: pause video when page is not visible
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            video.pause();
        } else {
            video.play().catch(function(error) {
                console.warn('Could not resume video playback:', error);
            });
        }
    });

    // Intersection Observer for performance optimization
    if ('IntersectionObserver' in window) {
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    video.play().catch(function(error) {
                        console.warn('Could not start video playback:', error);
                    });
                } else {
                    video.pause();
                }
            });
        }, {
            threshold: 0.1
        });

        observer.observe(video);
    }

    // Handle reduced motion preference
    if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        video.pause();
        video.style.display = 'none';
    }

    // Mobile optimization
    if ('connection' in navigator) {
        const connection = navigator.connection;
        if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
            video.pause();
            video.style.display = 'none';
        }
    }
});
