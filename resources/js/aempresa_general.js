/**
 * Company General Information Form JavaScript
 * Handles client-side validation and user interaction for institutional content
 */

document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('company-general-form');
    const quienesSomosInput = document.getElementById('quienes_somos');
    const misionInput = document.getElementById('mision');
    const visionInput = document.getElementById('vision');

    // Minimum content length for validation
    const MIN_CONTENT_LENGTH = 10;
    const MAX_CONTENT_LENGTH = 5000;

    /**
     * Validate textarea content
     */
    function validateTextarea(input, fieldName) {
        const value = input.value.trim();

        // Content is optional, so empty is valid
        if (value === '') {
            setFieldValid(input);
            return true;
        }

        // Check minimum length
        if (value.length < MIN_CONTENT_LENGTH) {
            setFieldInvalid(input, `El contenido de ${fieldName} debe tener al menos ${MIN_CONTENT_LENGTH} caracteres.`);
            return false;
        }

        // Check maximum length
        if (value.length > MAX_CONTENT_LENGTH) {
            setFieldInvalid(input, `El contenido de ${fieldName} no puede exceder ${MAX_CONTENT_LENGTH} caracteres.`);
            return false;
        }

        // Check for basic content quality (not just spaces or repeated characters)
        if (!/[a-zA-ZáéíóúÁÉÍÓÚñÑ]/.test(value)) {
            setFieldInvalid(input, `El contenido de ${fieldName} debe incluir texto válido.`);
            return false;
        }

        setFieldValid(input);
        return true;
    }

    /**
     * Validate that at least one field has content
     */
    function validateAtLeastOneField() {
        const quienesSomosValue = quienesSomosInput.value.trim();
        const misionValue = misionInput.value.trim();
        const visionValue = visionInput.value.trim();

        return quienesSomosValue.length >= MIN_CONTENT_LENGTH || 
               misionValue.length >= MIN_CONTENT_LENGTH || 
               visionValue.length >= MIN_CONTENT_LENGTH;
    }

    /**
     * Set field as valid
     */
    function setFieldValid(input) {
        input.classList.remove('is-invalid');
        input.classList.add('is-valid');

        const feedback = input.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.style.display = 'none';
        }
    }

    /**
     * Set field as invalid
     */
    function setFieldInvalid(input, message) {
        input.classList.remove('is-valid');
        input.classList.add('is-invalid');

        const feedback = input.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.textContent = message;
            feedback.style.display = 'block';
        }
    }

    /**
     * Clear field validation state
     */
    function clearFieldValidation(input) {
        input.classList.remove('is-valid', 'is-invalid');

        const feedback = input.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.style.display = 'none';
        }
    }

    /**
     * Update character counter for textarea
     */
    function updateCharacterCounter(input) {
        const value = input.value;
        const length = value.length;
        
        // Find or create character counter
        let counter = input.parentNode.querySelector('.character-counter');
        if (!counter) {
            counter = document.createElement('div');
            counter.className = 'character-counter';
            input.parentNode.appendChild(counter);
        }

        counter.textContent = `${length}/${MAX_CONTENT_LENGTH} caracteres`;

        // Update counter styling based on length
        counter.classList.remove('warning', 'danger');
        if (length > MAX_CONTENT_LENGTH * 0.9) {
            counter.classList.add('danger');
        } else if (length > MAX_CONTENT_LENGTH * 0.8) {
            counter.classList.add('warning');
        }
    }

    /**
     * Initialize form - clear all validation states on page load
     */
    function initializeForm() {
        const allInputs = [quienesSomosInput, misionInput, visionInput];

        allInputs.forEach(input => {
            if (input) {
                clearFieldValidation(input);
                updateCharacterCounter(input);
            }
        });
    }

    /**
     * Auto-resize textarea based on content
     */
    function autoResizeTextarea(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.max(textarea.scrollHeight, 120) + 'px';
    }

    // Real-time validation and character counting
    [quienesSomosInput, misionInput, visionInput].forEach((input, index) => {
        if (input) {
            const fieldNames = ['Quiénes Somos', 'Misión', 'Visión'];
            const fieldName = fieldNames[index];

            // Validation on blur
            input.addEventListener('blur', function() {
                validateTextarea(this, fieldName);
            });

            // Clear validation state while typing
            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid')) {
                    clearFieldValidation(this);
                }
                updateCharacterCounter(this);
                autoResizeTextarea(this);
            });

            // Auto-resize on focus
            input.addEventListener('focus', function() {
                autoResizeTextarea(this);
            });

            // Initial auto-resize
            autoResizeTextarea(input);
        }
    });

    // Form submission validation
    if (form) {
        form.addEventListener('submit', function(event) {
            let isValid = true;
            const fieldNames = ['Quiénes Somos', 'Misión', 'Visión'];

            // Validate all fields
            [quienesSomosInput, misionInput, visionInput].forEach((input, index) => {
                if (input && !validateTextarea(input, fieldNames[index])) {
                    isValid = false;
                }
            });

            // Check that at least one field has content
            if (isValid && !validateAtLeastOneField()) {
                isValid = false;
                
                // Show error on all fields
                [quienesSomosInput, misionInput, visionInput].forEach((input, index) => {
                    if (input) {
                        setFieldInvalid(input, 'Debe completar al menos uno de los campos de contenido institucional.');
                    }
                });
            }

            // Prevent form submission if validation fails
            if (!isValid) {
                event.preventDefault();
                event.stopPropagation();

                // Focus the first invalid field
                const firstInvalid = form.querySelector('.is-invalid');
                if (firstInvalid) {
                    firstInvalid.focus();
                }

                // Show error message
                if (typeof showSweetAlertError === 'function') {
                    showSweetAlertError('Error de Validación', 'Por favor, corrija los errores en el formulario antes de continuar.');
                }
            } else {
                // Add loading state
                form.classList.add('loading');
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin fa-fw me-1"></i> Guardando...';

                    // Restore button state if form submission fails (fallback)
                    setTimeout(() => {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalText;
                        form.classList.remove('loading');
                    }, 10000); // 10 seconds timeout
                }
            }
        });
    }

    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert-dismissible');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert && alert.parentNode) {
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 300);
            }
        }, 5000);
    });

    // Initialize form - clear any validation states on page load
    initializeForm();

    console.log('Company general information form initialized successfully.');
});
