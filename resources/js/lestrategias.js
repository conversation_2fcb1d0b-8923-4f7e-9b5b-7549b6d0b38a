/**
 * ESTRATEGIAS ADMIN MODULE JAVASCRIPT
 * Handles CRUD operations for estrategias management
 */

$(document).ready(function() {
    // Initialize form handlers
    initializeFormHandlers();

    // Initialize modal handlers
    initializeModalHandlers();
});

/**
 * Initialize form submission handlers
 */
function initializeFormHandlers() {
    $('#formEstrategia').on('submit', function(e) {
        e.preventDefault();
        
        // Clear previous validation states
        clearValidationStates();
        
        // Validate form
        if (!validateForm()) {
            return;
        }
        
        // Show loading state
        showLoadingState(true);
        
        // Prepare form data
        const formData = new FormData(this);
        
        // Submit form via AJAX
        $.ajax({
            url: 'listado-estrategias',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                handleFormSuccess(response);
            },
            error: function(xhr, status, error) {
                handleFormError(xhr, status, error);
            },
            complete: function() {
                showLoadingState(false);
            }
        });
    });
}

/**
 * Initialize modal event handlers
 */
function initializeModalHandlers() {
    // Reset form when modal is hidden
    $('#modalEstrategia').on('hidden.bs.modal', function() {
        resetForm();
    });
}

/**
 * Open modal for creating new estrategia
 */
function abrirModalCrear() {
    resetForm();
    $('#modalEstrategiaLabel').text('Crear Nueva Estrategia');
    $('#action').val('crear');
    $('#btnSubmit').text('Crear');
}

/**
 * Open modal for editing existing estrategia
 */
function abrirModalEditar(estrategiaId) {
    resetForm();
    $('#modalEstrategiaLabel').text('Editar Estrategia');
    $('#action').val('editar');
    $('#estrategiaId').val(estrategiaId);
    $('#btnSubmit').text('Actualizar');
    
    // Show loading state
    showLoadingState(true);
    
    $.ajax({
        url: 'listado-estrategias',
        type: 'POST',
        data: {
            action: 'obtener',
            id: estrategiaId
        },
        dataType: 'json',
        success: function(response) {
            if (response.success && response.estrategia) {
                const estrategia = response.estrategia;
                
                // Populate form fields
                $('#titulo').val(estrategia.titulo);
                $('#texto').val(estrategia.texto);
                $('#prioridad').val(estrategia.prioridad);
                
                // Show modal
                $('#modalEstrategia').modal('show');
            } else {
                showSweetAlertError('Error', response.message || 'No se pudo cargar los datos de la estrategia.');
            }
        },
        error: function(xhr, status, error) {
            showSweetAlertError('Error', 'Error al cargar los datos de la estrategia.');
        },
        complete: function() {
            showLoadingState(false);
        }
    });
}

/**
 * Delete estrategia with confirmation
 */
function eliminarEstrategia(estrategiaId) {
    swal({
        title: '¿Estás seguro?',
        text: 'Esta acción eliminará la estrategia permanentemente.',
        icon: 'warning',
        buttons: {
            cancel: {
                text: 'Cancelar',
                value: null,
                visible: true,
                className: 'btn-secondary',
                closeModal: true
            },
            confirm: {
                text: 'Sí, eliminar',
                value: true,
                visible: true,
                className: 'btn-danger',
                closeModal: true
            }
        },
        dangerMode: true
    }).then((willDelete) => {
        if (willDelete) {
            $.ajax({
                url: 'listado-estrategias',
                type: 'POST',
                data: {
                    action: 'eliminar',
                    id: estrategiaId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showSweetAlertSuccess('¡Eliminado!', response.message);
                        // Reload page to refresh the table
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        showSweetAlertError('Error', response.message);
                    }
                },
                error: function(xhr, status, error) {
                    showSweetAlertError('Error', 'Error al eliminar la estrategia.');
                }
            });
        }
    });
}

/**
 * Validate form before submission
 */
function validateForm() {
    let isValid = true;
    
    // Validate title
    const titulo = $('#titulo').val().trim();
    if (!titulo) {
        showFieldError('titulo', 'El título es requerido.');
        isValid = false;
    } else if (titulo.length < 2) {
        showFieldError('titulo', 'El título debe tener al menos 2 caracteres.');
        isValid = false;
    } else if (titulo.length > 100) {
        showFieldError('titulo', 'El título no puede exceder 100 caracteres.');
        isValid = false;
    }
    
    // Validate description
    const texto = $('#texto').val().trim();
    if (!texto) {
        showFieldError('texto', 'La descripción es requerida.');
        isValid = false;
    } else if (texto.length < 10) {
        showFieldError('texto', 'La descripción debe tener al menos 10 caracteres.');
        isValid = false;
    } else if (texto.length > 1000) {
        showFieldError('texto', 'La descripción no puede exceder 1000 caracteres.');
        isValid = false;
    }
    
    // Validate priority
    const prioridad = parseInt($('#prioridad').val());
    if (!prioridad || prioridad < 1) {
        showFieldError('prioridad', 'La prioridad es requerida y debe ser mayor a 0.');
        isValid = false;
    } else if (prioridad > 999) {
        showFieldError('prioridad', 'La prioridad no puede ser mayor a 999.');
        isValid = false;
    }
    
    return isValid;
}

/**
 * Handle successful form submission
 */
function handleFormSuccess(response) {
    if (response.success) {
        // Hide modal
        $('#modalEstrategia').modal('hide');
        
        // Show success message
        showSweetAlertSuccess('¡Éxito!', response.message);
        
        // Reload page to refresh the table
        setTimeout(() => {
            location.reload();
        }, 1500);
    } else {
        showSweetAlertError('Error', response.message);
    }
}

/**
 * Handle form submission error
 */
function handleFormError(xhr, status, error) {
    console.error('Form submission error:', error);
    showSweetAlertError('Error', 'Error al procesar la solicitud. Por favor, intente de nuevo.');
}

/**
 * Show field validation error
 */
function showFieldError(fieldId, message) {
    const field = $('#' + fieldId);
    const errorDiv = $('#' + fieldId + '-error');
    
    field.addClass('is-invalid');
    errorDiv.text(message);
}

/**
 * Clear all validation states
 */
function clearValidationStates() {
    $('.form-control').removeClass('is-invalid');
    $('.invalid-feedback').text('');
}

/**
 * Reset form to initial state
 */
function resetForm() {
    $('#formEstrategia')[0].reset();
    $('#estrategiaId').val('');
    clearValidationStates();
}

/**
 * Show/hide loading state
 */
function showLoadingState(show) {
    const spinner = $('#loadingSpinner');
    const submitBtn = $('#btnSubmit');
    
    if (show) {
        spinner.removeClass('d-none');
        submitBtn.prop('disabled', true);
    } else {
        spinner.addClass('d-none');
        submitBtn.prop('disabled', false);
    }
}

/**
 * Show SweetAlert success message
 */
function showSweetAlertSuccess(title, message) {
    swal({
        title: title,
        text: message,
        icon: 'success',
        button: 'OK'
    });
}

/**
 * Show SweetAlert error message
 */
function showSweetAlertError(title, message) {
    swal({
        title: title,
        text: message,
        icon: 'error',
        button: 'OK'
    });
}
