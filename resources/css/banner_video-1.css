/* Custom Video Banner - Clean Implementation with No Theme Conflicts */

/* Main banner container */
.custom-video-banner {
    position: relative;
    width: 100%;
    min-height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
}

/* Video container wrapper */
.custom-video-container {
    position: relative;
    width: 100%;
    height: 100vh;
    min-height: 600px;
}

/* Video background */
.custom-video-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
    pointer-events: none;
}

/* Video overlay for text readability */
.custom-video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2;
    pointer-events: none;
}

/* Content container */
.custom-video-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    z-index: 3;
}

/* Banner text container */
.custom-banner-text {
    position: relative;
    z-index: 4;
}

/* Banner title styling */
.custom-banner-title {
    font-size: 45px;
    line-height: 1.2em;
    text-transform: none;
    color: white;
    font-family: "Exo 2", sans-serif;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    margin-bottom: 20px;
}

/* Banner description styling */
.custom-banner-description {
    color: white;
    text-align: justify;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    margin-bottom: 15px;
    font-size: 22px;
    line-height: 1.6;
}

/* Banner button container */
.custom-banner-button {
    position: relative;
    z-index: 5;
    margin-top: 30px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .custom-video-container {
        height: 80vh;
        min-height: 500px;
    }

    .custom-video-overlay {
        background: rgba(0, 0, 0, 0.6);
    }

    .custom-banner-title {
        font-size: 28px;
        text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.9);
    }

    .custom-banner-description {
        font-size: 14px;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9);
    }
}

@media (max-width: 575px) {
    .custom-video-container {
        height: 70vh;
        min-height: 450px;
    }

    .custom-video-overlay {
        background: rgba(0, 0, 0, 0.7);
    }

    .custom-banner-title {
        font-size: 21px;
    }

    .custom-banner-description {
        font-size: 16px;
    }
}

/* Fallback background for loading and video failure */
.custom-video-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1496D7 0%, #6FA4C4 100%);
    z-index: 0;
}

/* Hide fallback once video loads */
.custom-video-bg[data-loaded="true"] ~ .custom-video-container::before {
    display: none;
}

/* Video controls hidden */
.custom-video-bg::-webkit-media-controls {
    display: none;
}

.custom-video-bg::-webkit-media-controls-panel {
    display: none;
}

/* Performance optimization - reduced motion */
@media (prefers-reduced-motion: reduce) {
    .custom-video-bg {
        display: none;
    }

    .custom-video-container::before {
        display: block;
    }
}

/* Video positioning for different orientations */
@media (orientation: portrait) {
    .custom-video-bg {
        object-position: center center;
    }
}

@media (orientation: landscape) {
    .custom-video-bg {
        object-position: center center;
    }
}

/* mobile */
@media (max-width: 575px) {
    .custom-video-banner {
        min-height: 0;
        margin-top: 20px;
    }
}


/* Additional styling for better integration */
.custom-banner-text {
    padding: 20px 0;
}

/* Ensure proper spacing */
.custom-banner-title {
    margin-bottom: 25px;
}

.custom-banner-description:last-of-type {
    margin-bottom: 30px;
}
