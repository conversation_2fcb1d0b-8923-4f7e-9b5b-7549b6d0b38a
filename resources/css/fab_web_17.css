:root {
    /*--azul-claro-empresarial: #3FA2DE;*/
    --azul-claro-empresarial: #6FA4C4;
    --azul-oscuro-empresarial: #1496D7;
}

header a {
    text-transform: none !important;
}

.service-details-wrapper .icon-title h4,
.service-details-wrapper .icon h4 {
    font-size: 15px;
    text-transform: none !important;
}

.service-details-wrapper .icon-title i,
.service-details-wrapper .icon i {
    font-size: 35px;
}

.service-details-wrapper .single-service-card .content,
.service-details-wrapper .single-service-box .content {
    padding: 1rem !important;
}

.service-details-wrapper .single-service-card .content p,
.service-details-wrapper .single-service-box .contents p {
    text-align: justify;
    font-size: 12px;
    line-height: 1.6em;
}

.service-details-wrapper .single-service-box .contents p{
    margin-top: 5px !important;
}

.service-details-wrapper .single-service-card,
.service-details-wrapper .single-service-box {
    border: 1px solid #EBEDF2;
    background-color: #fff;
    margin-top: 10px !important;
}

.single-service-box .icon{
    display: flex !important;
    align-items: center !important;
}

.single-service-box .icon h4{
    margin-bottom: 0 !important;
    padding-left: 10px !important;
}

.single-service-box {
    padding: 15px !important;
}

.single-service-box:hover h4,
.single-service-box:hover .icon i{
    color: white !important;
}

.single-service-card .content .icon-title .icon,
.single-service-box .icon i {
    color: var(--azul-oscuro-empresarial) !important;
}

.service-details-wrapper .service-title {
    padding-top: 15px !important;
}

.service-details-wrapper .parrafo{
    text-align: justify;
}

.service-details-wrapper .service-details-sidebar .single-service-sidebar ul li a.active {
    background-color: #FF5E14 !important;
    color: #fff !important;
}

.service-details-wrapper .single-service-box{
    height: 220px !important;
}

.service-details-sidebar .single-service-sidebar ul li a {
    text-transform: none !important;
    font-size: 12px;
}

.single-icon-circle-item .icon {
    color: var(--azul-oscuro-empresarial) !important;
}

.single-icon-circle-item .icon::before {
    border: 5px solid var(--azul-oscuro-empresarial) !important;
}

#empresas p{
    text-align: justify !important;
}

section#aliado_estrategico {
    background-color: #ececec !important;
}

#aliado_estrategico .process__widget .icons {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #FFFFFF;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    box-shadow: 0px 0px 10px rgba(255, 94, 20, 0.15);
    font-size: 45px;
}

#aliado_estrategico .process__widget .icon__widget {
    margin: 0 auto;
    width: 130px;
    height: 130px;
    border-radius: 50%;
    background-color: transparent;
    border: 1px dashed var(--azul-oscuro-empresarial) !important;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30px;
}

#aliado_estrategico .pilar-titulo{
    background-color: white;
    padding: 37px;
    border-radius: 10px;
    text-align: center;
    font-size: 20px;
    font-weight: 500 !important;
    color: var(--azul-oscuro-empresarial) !important;
}

#aliado_estrategico .pilar-parrafo{
    background-color: white;
    padding: 37px;
    border-radius: 10px;
    text-align: justify;
    font-size: 14px;
    margin-left: 12px !important;
    height: 230px;
}

#aliado_estrategico .pilar-fila{
    padding-right: 22px !important;
}

#aliado_estrategico .section-title-2 h1 {
    font-size: 34px !important;
}

.about-section .container p,
.our-service-wrapper .container p{
    text-align: justify !important;
}

#listado_valores .single-service-card{
    height: 270px !important;
}

#contactanos .live-chat p{
    text-align: justify !important;
}

#contactanos .live-chat h5,
#contactanos .contact-form h2{
    font-size: 18px !important;
}

#contactanos .contact-info h5{
    text-transform: none !important;
}

#contactanos .contact-us-sidebar,
#contactanos .contact-form{
    height: 630px !important;
}

.header-7 .main-menu ul > li > a.active {
    color: var(--azul-oscuro-empresarial) !important;
}

.header-7 .main-menu ul > li:hover > a {
    color: var(--azul-oscuro-empresarial) !important;
}

header .main-menu ul li a:hover {
    color: var(--azul-oscuro-empresarial) !important;
}

.rr-btn {
    background: var(--azul-oscuro-empresarial) !important;
}

.rr-btn__liquidBtn:hover .rr-btn__liquidBtn__animation {
    background: var(--azul-claro-empresarial) !important;
}

.banner-7__call span {
    background: var(--azul-oscuro-empresarial) !important;
}

.banner-7__call:hover {
    color: var(--azul-oscuro-empresarial) !important;
}

.element-7 {
    background: var(--azul-oscuro-empresarial) !important;
}

.header-7 .header-top-bar .top-left-content li a i {
    color: var(--azul-oscuro-empresarial) !important;
}

.header-7 .header-top-bar .top-left-content li a:hover {
    color: var(--azul-oscuro-empresarial) !important;
}

h1, h2, h3, h4, h5, h6 {
    color: var(--azul-oscuro-empresarial) !important;
}

.service-details-wrapper .service-details-sidebar .single-service-sidebar ul li a.active {
    background-color: var(--azul-oscuro-empresarial) !important;
}

.color-azul-oscuro-empresarial{
    color: var(--azul-oscuro-empresarial) !important;
}

#aliado_estrategico img {
    width: 60% !important;
}

.service-details-sidebar .single-service-sidebar ul li a {
    color: var(--azul-oscuro-empresarial) !important;
}

.block-contents .section-title span {
    color: var(--azul-claro-empresarial) !important;
}

.single-info .icon {
    color: var(--azul-oscuro-empresarial) !important;
}

.contact-form input[type=submit], .contact-form .submit-btn {
    background: var(--azul-oscuro-empresarial) !important;
}

.contact-form input[type=submit].active, .contact-form input[type=submit]:hover, .contact-form .submit-btn.active, .contact-form .submit-btn:hover {
    background-color: var(--azul-claro-empresarial) !important;
}

#scrollUp {
    background-color: var(--azul-oscuro-empresarial) !important;
}

#scrollUp:hover {
    background-color: var(--azul-claro-empresarial) !important;
}

.banner-7__title {
    font-size: 40px !important;
    line-height: 1.2em !important;
    text-transform: none !important;
    color: white !important;
    font-family: "Exo 2", sans-serif !important;
}

.banner-7__dec{
    text-align: justify !important;
}

@media (max-width: 575px) {
    .banner-7__title {
        font-size: 21px !important;
    }

    .banner-7__dec{
        font-size: 16px !important;
    }

    .side-info .side-info-content .offset__widget.offset__header .offset__logo img {
        width: 200px !important;
        max-width: none !important;
    }
}

#valores .single-service-card{
    cursor: pointer;
}

#valores .single-service-card:hover{
    background: #141820;
}

#valores .single-service-card:hover p,
#valores .single-service-card:hover .icon,
#valores .single-service-card:hover h4{
    color: white !important;
}

.side-info .side-info-content .offset__widget.offset__header .side-info-close {
    background-color: var(--azul-oscuro-empresarial) !important;
}

.mean-container .mean-nav ul li a.mean-expand {
    background: var(--azul-oscuro-empresarial) !important;
}

.mobile-nav__social a i:hover {
    background-color: var(--azul-oscuro-empresarial) !important;
}

.mean-container .mean-nav ul li a:hover {
    color: var(--azul-oscuro-empresarial) !important;
}

.servicio_descrip{
    cursor: pointer;
}

.servicio_descrip h4{
    font-size: 18px !important;
}

.servicio_descrip .contents p{
    font-size: 14px !important;
    font-weight: bold;
}

#footer_contacto .logo_redes:hover,
.header-7 .header-top-bar .top-social-icons a:hover{
    color: var(--azul-oscuro-empresarial) !important;
}

#footer_contacto .logo_redes_insta:hover,
.header-7 .header-top-bar .top-social-icons .icono_insta:hover{
    color: #fa7e1e !important;
}

.header-7 .header-top-bar .top-social-icons a {
    margin-left: 6px !important;
}
