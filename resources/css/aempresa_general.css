/* Company General Information Form Styles */

/* Configuration Panel Styling */
#company-general-form .panel {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: none;
    margin-bottom: 20px;
}

#company-general-form .panel-heading {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
}

#company-general-form .panel-title {
    font-weight: 600;
    font-size: 1.1rem;
}

/* Form Field Styling */
#company-general-form .form-control {
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 12px 15px;
    font-size: 14px;
    transition: all 0.3s ease;
    resize: vertical;
    min-height: 120px;
}

#company-general-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

#company-general-form .form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

#company-general-form .form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* Textarea Specific Styling */
#company-general-form textarea.form-control {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    resize: vertical;
}

#company-general-form textarea.form-control::placeholder {
    color: #6c757d;
    opacity: 0.7;
}

/* Form Labels */
#company-general-form .form-label {
    font-weight: 600;
    /*color: #495057;*/
    margin-bottom: 8px;
    font-size: 1rem;
}

/* Help Text */
#company-general-form .form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 5px;
}

/* Validation Feedback */
#company-general-form .invalid-feedback {
    display: none;
    width: 100%;
    margin-top: 5px;
    font-size: 0.875rem;
    color: #dc3545;
}

#company-general-form .valid-feedback {
    display: block;
    width: 100%;
    margin-top: 5px;
    font-size: 0.875rem;
    color: #28a745;
}

/* Info Alert Styling */
#company-general-form .alert-info {
    background-color: #e3f2fd;
    border-color: #bbdefb;
    color: #0277bd;
    border-radius: 6px;
    padding: 15px;
    margin-top: 20px;
}

#company-general-form .alert-info i {
    margin-right: 8px;
}

/* Success/Error Alerts */
.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.alert i {
    margin-right: 8px;
}

/* Submit Button Styling */
#company-general-form .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 12px 25px;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.3s ease;
}

#company-general-form .btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

#company-general-form .btn-primary:active {
    transform: translateY(0);
}

/* Field Spacing */
#company-general-form .mb-4 {
    margin-bottom: 2rem;
}

/* Character Counter (if needed) */
.character-counter {
    font-size: 0.75rem;
    color: #6c757d;
    text-align: right;
    margin-top: 5px;
}

.character-counter.warning {
    color: #ffc107;
}

.character-counter.danger {
    color: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
    #company-general-form .panel-body {
        padding: 20px 15px;
    }

    #company-general-form .panel-footer {
        padding: 15px;
        text-align: center;
    }

    #company-general-form .btn-primary {
        width: 100%;
    }

    #company-general-form textarea.form-control {
        min-height: 100px;
    }
}

/* Loading State */
#company-general-form.loading .btn-primary {
    opacity: 0.6;
    cursor: not-allowed;
}

#company-general-form.loading .btn-primary:hover {
    transform: none;
    box-shadow: none;
}

#company-general-form.loading textarea {
    opacity: 0.7;
    pointer-events: none;
}

/* Smooth Animations */
#company-general-form .form-control,
#company-general-form .btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus Ring for Accessibility */
#company-general-form .form-control:focus-visible {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Panel Expand/Collapse Animation */
#company-general-form .panel-body {
    transition: all 0.3s ease;
}

/* Textarea Focus Enhancement */
/*#company-general-form textarea:focus {
    background-color: #fafbfc;
}*/

/* Field Group Styling */
#company-general-form .field-group {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

#company-general-form .field-group .form-label {
    color: #495057;
    font-weight: 600;
}
