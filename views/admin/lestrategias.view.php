<?php
#region region DOCS

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title>Gestión de Estrategias | Admin</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/head.view.php'; ?>
	<link href="<?php echo RUTA_RESOURCES ?>css/dashboard.css" rel="stylesheet"/>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed ">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/sidebar.view.php'; ?>
	<!-- END #sidebar -->
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		
		<!-- BEGIN page-header -->
		<h1 class="page-header">Gestión de Estrategias <small>Administrar estrategias corporativas</small></h1>
		<!-- END page-header -->
		
		<?php #region region ESTRATEGIAS FORM ?>
		<div class="panel panel-inverse no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">Gestión de Estrategias</h4>
				<div class="panel-heading-btn">
					<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
					<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
				</div>
			</div>
			<div class="panel-body">
				<div class="row mb-3">
					<div class="col-12 text-end">
						<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalEstrategia" onclick="abrirModalCrear()">
							<i class="fa fa-plus fa-fw me-1"></i> Crear Nueva Estrategia
						</button>
					</div>
				</div>
				
				<div class="table-responsive">
					<table class="table table-bordered align-middle">
						<thead>
						<tr>
							<th width="120" class="text-center">Acciones</th>
							<th>Título</th>
							<th>Descripción</th>
							<th width="100" class="text-center">Prioridad</th>
						</tr>
						</thead>
						<tbody>
						<?php if (!empty($estrategias)): ?>
							<?php foreach ($estrategias as $estrategia): ?>
								<tr>
									<td class="text-center">
										<button type="button" class="btn btn-xs btn-warning me-1"
										        onclick="abrirModalEditar(<?php echo $estrategia->getId(); ?>)"
										        title="Editar estrategia">
											<i class="fa fa-edit"></i>
										</button>
										<button type="button" class="btn btn-xs btn-danger"
										        onclick="eliminarEstrategia(<?php echo $estrategia->getId(); ?>)"
										        title="Eliminar estrategia">
											<i class="fa fa-trash"></i>
										</button>
									</td>
									<td>
										<strong><?php echo htmlspecialchars($estrategia->getTitulo()); ?></strong>
									</td>
									<td>
										<div style="max-width: 400px; word-wrap: break-word; white-space: normal;">
											<?php echo htmlspecialchars($estrategia->getTexto()); ?>
										</div>
									</td>
									<td class="text-center">
										<span class="badge bg-primary"><?php echo $estrategia->getPrioridad(); ?></span>
									</td>
								</tr>
							<?php endforeach; ?>
						<?php else: ?>
							<tr>
								<td colspan="4" class="text-center text-muted">
									<i class="fa fa-info-circle me-2"></i>No hay estrategias registradas
								</td>
							</tr>
						<?php endif; ?>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<?php #endregion ESTRATEGIAS FORM ?>
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="javascript:;" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region MODAL ESTRATEGIA ?>
<!-- Modal for Create/Edit Estrategia -->
<div class="modal fade" id="modalEstrategia" tabindex="-1" aria-labelledby="modalEstrategiaLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="modalEstrategiaLabel">Crear Nueva Estrategia</h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<form id="formEstrategia" novalidate>
				<div class="modal-body">
					<input type="hidden" id="estrategiaId" name="id" value="">
					<input type="hidden" id="action" name="action" value="crear">
					
					<!-- Title Field -->
					<div class="mb-3">
						<label for="titulo" class="form-label">Título <span class="text-danger">*</span></label>
						<input type="text" class="form-control" id="titulo" name="titulo" maxlength="100" required>
						<div class="invalid-feedback" id="titulo-error"></div>
					</div>
					
					<!-- Description Field -->
					<div class="mb-3">
						<label for="texto" class="form-label">Descripción <span class="text-danger">*</span></label>
						<textarea class="form-control" id="texto" name="texto" rows="7" maxlength="1000" required></textarea>
						<div class="invalid-feedback" id="texto-error"></div>
					</div>
					
					<!-- Priority Field -->
					<div class="mb-3">
						<label for="prioridad" class="form-label">Prioridad <span class="text-danger">*</span></label>
						<input type="number" class="form-control" id="prioridad" name="prioridad" min="1" max="999" required>
						<div class="invalid-feedback" id="prioridad-error"></div>
					</div>
					
					<div class="alert alert-info">
						<i class="fa fa-info-circle"></i>
						<strong>Nota:</strong> Las estrategias se mostrarán en el sitio web ordenadas por prioridad (menor a mayor).
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
					<button type="submit" class="btn btn-primary" id="btnSubmit">
						<span class="spinner-border spinner-border-sm me-1 d-none" id="loadingSpinner"></span>
						Crear
					</button>
				</div>
			</form>
		</div>
	</div>
</div>
<?php #endregion MODAL ESTRATEGIA ?>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/core_js.view.php'; ?>

<script src="<?php echo RUTA_RESOURCES ?>js/lestrategias.js"></script>

<?php #endregion JS ?>
</body>
</html>
