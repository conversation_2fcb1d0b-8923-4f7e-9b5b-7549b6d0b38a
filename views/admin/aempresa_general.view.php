<?php
#region region DOCS

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Información General de la Empresa</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/head.view.php'; ?>
	<link href="<?php echo RUTA_RESOURCES ?>css/aempresa_general.css" rel="stylesheet" />
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<h4>Información General de la Empresa</h4>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region SUCCESS/ERROR MESSAGES ?>
		<?php if ($success_display === 'show'): ?>
			<div class="alert alert-success alert-dismissible fade show" role="alert">
				<i class="fa fa-check-circle me-2"></i>
				<?php echo htmlspecialchars($success_text); ?>
				<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
			</div>
		<?php endif; ?>

		<?php if ($error_display === 'show'): ?>
			<div class="alert alert-danger alert-dismissible fade show" role="alert">
				<i class="fa fa-exclamation-triangle me-2"></i>
				<?php echo htmlspecialchars($error_text); ?>
				<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
			</div>
		<?php endif; ?>
		<?php #endregion SUCCESS/ERROR MESSAGES ?>

		<?php #region region COMPANY GENERAL FORM ?>
		<form action="nuestra-empresa-general" method="POST" id="company-general-form" novalidate>
			<div class="panel panel-inverse no-border-radious">
				<div class="panel-heading no-border-radious">
					<h4 class="panel-title">Contenido Institucional</h4>
					<div class="panel-heading-btn">
						<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
						<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
					</div>
				</div>
				<div class="panel-body">
					<div class="row">
						<div class="col-12">
							<?php #region region QUIENES SOMOS FIELD ?>
							<div class="mb-4">
								<label for="quienes_somos" class="form-label">Quiénes Somos:</label>
								<textarea id="quienes_somos"
										  name="quienes_somos"
										  rows="10"
										  class="form-control"
										  placeholder="Ingrese la información sobre quiénes somos..."><?php echo htmlspecialchars($configData ? $configData->getQuienesSomos() ?? '' : '', ENT_QUOTES, 'UTF-8'); ?></textarea>
								<div class="invalid-feedback">
									Por favor, ingrese información válida sobre quiénes somos.
								</div>
								<small class="form-text text-muted">
									Describa la identidad, historia y propósito de la empresa.
								</small>
							</div>
							<?php #endregion QUIENES SOMOS FIELD ?>

							<?php #region region MISION FIELD ?>
							<div class="mb-4">
								<label for="mision" class="form-label">Misión:</label>
								<textarea id="mision"
										  name="mision"
										  rows="10"
										  class="form-control"
										  placeholder="Ingrese la misión de la empresa..."><?php echo htmlspecialchars($configData ? $configData->getMision() ?? '' : '', ENT_QUOTES, 'UTF-8'); ?></textarea>
								<div class="invalid-feedback">
									Por favor, ingrese una misión válida.
								</div>
								<small class="form-text text-muted">
									Defina el propósito fundamental y la razón de ser de la empresa.
								</small>
							</div>
							<?php #endregion MISION FIELD ?>

							<?php #region region VISION FIELD ?>
							<div class="mb-4">
								<label for="vision" class="form-label">Visión:</label>
								<textarea id="vision"
										  name="vision"
										  rows="10"
										  class="form-control"
										  placeholder="Ingrese la visión de la empresa..."><?php echo htmlspecialchars($configData ? $configData->getVision() ?? '' : '', ENT_QUOTES, 'UTF-8'); ?></textarea>
								<div class="invalid-feedback">
									Por favor, ingrese una visión válida.
								</div>
								<small class="form-text text-muted">
									Describa hacia dónde se dirige la empresa y qué aspira a lograr en el futuro.
								</small>
							</div>
							<?php #endregion VISION FIELD ?>
						</div>
					</div>

					<div class="row">
						<div class="col-12">
							<div class="alert alert-info">
								<i class="fa fa-info-circle"></i>
								<strong>Nota:</strong> Complete al menos uno de los campos para guardar la información. Puede actualizar estos contenidos en cualquier momento.
							</div>
						</div>
					</div>
				</div>
				<div class="panel-footer text-end">
					<button type="submit" class="btn btn-primary">
						<i class="fa fa-save fa-fw me-1"></i> Guardar cambios
					</button>
				</div>
			</div>
		</form>
		<?php #endregion COMPANY GENERAL FORM ?>
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/core_js.view.php'; ?>

<script src="<?php echo RUTA_RESOURCES ?>js/aempresa_general.js"></script>

<?php #endregion JS ?>

</body>
</html>
