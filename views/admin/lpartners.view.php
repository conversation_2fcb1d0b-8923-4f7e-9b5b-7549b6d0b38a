<?php
#region region DOCS

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
    <meta charset="utf-8" />
    <title>Partners | <?php echo APP_NAME; ?></title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
    <meta content="" name="description" />
    <meta content="" name="author" />

    <?php require_once __ROOT__ . '/views/admin/head.view.php'; ?>
    <link href="<?php echo RUTA_RESOURCES ?>css/lpartners.css" rel="stylesheet" />
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<h4>Partners</h4>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region PARTNERS FORM ?>
		<div class="panel panel-inverse no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">Gestión de Partners</h4>
				<div class="panel-heading-btn">
					<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
					<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
				</div>
			</div>
			<div class="panel-body">
				<div class="row mb-3">
					<div class="col-12 text-end">
						<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalPartner" onclick="abrirModalCrear()">
							<i class="fa fa-plus fa-fw me-1"></i> Crear Nuevo Partner
						</button>
					</div>
				</div>

				<div class="table-responsive">
					<table class="table table-bordered align-middle">
						<thead>
							<tr>
								<th width="120" class="text-center">Acciones</th>
								<th width="150" class="text-center">Imagen</th>
								<th width="100" class="text-center">Prioridad</th>
							</tr>
						</thead>
						<tbody>
							<?php if (!empty($partners)): ?>
								<?php foreach ($partners as $partner): ?>
								<tr>
									<td class="text-center">
										<button type="button" class="btn btn-xs btn-warning me-1"
												onclick="abrirModalEditar(<?php echo $partner->getId(); ?>)"
												title="Editar partner">
											<i class="fa fa-edit"></i>
										</button>
										<button type="button" class="btn btn-xs btn-danger"
												onclick="eliminarPartner(<?php echo $partner->getId(); ?>)"
												title="Eliminar partner">
											<i class="fa fa-trash"></i>
										</button>
									</td>
									<td class="text-center">
										<?php if ($partner->getImagen()): ?>
											<img src="<?php echo RUTA_RESOURCES ?>images/partners/<?php echo htmlspecialchars($partner->getImagen()); ?>"
												 alt="Partner" class="partner-thumbnail" style="max-width: 100px; max-height: 50px; object-fit: contain;">
										<?php else: ?>
											<span class="text-muted">Sin imagen</span>
										<?php endif; ?>
									</td>
									<td class="text-center">
										<span class="badge bg-primary"><?php echo $partner->getPrioridad(); ?></span>
									</td>
								</tr>
								<?php endforeach; ?>
							<?php else: ?>
								<tr>
									<td colspan="3" class="text-center text-muted">
										<i class="fa fa-info-circle me-2"></i>No hay partners registrados
									</td>
								</tr>
							<?php endif; ?>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<?php #endregion PARTNERS FORM ?>
	</div>
	<!-- END #content -->

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

    <?php #region MODAL PARTNER ?>
    <!-- Modal for Create/Edit Partner -->
    <div class="modal fade" id="modalPartner" tabindex="-1" aria-labelledby="modalPartnerLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalPartnerLabel">Crear Nuevo Partner</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="formPartner" novalidate enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" id="partnerId" name="id" value="">
                        <input type="hidden" id="action" name="action" value="crear">

                        <div class="mb-3">
                            <label for="imagen" class="form-label">
                                Imagen <span class="text-danger">*</span>
                            </label>
                            <input type="file" class="form-control" id="imagen" name="imagen" 
                                   accept="image/*" required>
                            <div class="invalid-feedback"></div>
                            <div class="form-text">
                                <strong>Recomendaciones:</strong><br>
                                • Dimensiones recomendadas: 800 x 128 píxeles<br>
                                • Tamaño máximo: 600KB<br>
                                • Formatos permitidos: JPG, PNG, GIF, WebP
                            </div>
                            <div id="currentImagePreview" class="mt-2" style="display: none;">
                                <label class="form-label">Imagen actual:</label><br>
                                <img id="currentImage" src="" alt="Imagen actual" style="max-width: 200px; max-height: 100px; object-fit: contain;">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="prioridad" class="form-label">
                                Prioridad <span class="text-danger">*</span>
                            </label>
                            <input type="number" class="form-control" id="prioridad" name="prioridad" 
                                   required min="1" max="999" placeholder="Ingrese la prioridad">
                            <div class="invalid-feedback"></div>
                            <div class="form-text">Número entero positivo para ordenar los partners (1-999)</div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i>
                            <strong>Nota:</strong> Los partners se mostrarán en el sitio web ordenados por prioridad (menor a mayor).
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary" id="btnSubmit">
                            <span class="spinner-border spinner-border-sm me-1 d-none" id="loadingSpinner"></span>
                            Crear
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php #endregion MODAL PARTNER ?>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/core_js.view.php'; ?>

<script src="<?php echo RUTA_RESOURCES ?>js/lpartners.js"></script>

<?php #endregion JS ?>
</body>
</html>
