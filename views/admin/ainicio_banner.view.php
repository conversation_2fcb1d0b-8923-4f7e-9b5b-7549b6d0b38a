<?php
#region region DOCS
/** @var ConfigWeb $configData */

use App\classes\ConfigWeb;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Banner de Inicio</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/head.view.php'; ?>
	<link href="<?php echo RUTA_RESOURCES ?>css/dashboard.css" rel="stylesheet" />
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<h4>Banner de Inicio</h4>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region BANNER FORM ?>
		<form action="inicio-banner" method="POST" id="banner-form" enctype="multipart/form-data" novalidate>
			<div class="panel panel-inverse no-border-radious">
				<div class="panel-heading no-border-radious">
					<h4 class="panel-title">Configuración del Banner de Inicio</h4>
					<div class="panel-heading-btn">
						<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
						<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
					</div>
				</div>
				<div class="panel-body">
					<div class="row">
						<div class="col-md-12">
							<?php #region region TITULO BANNER FIELD ?>
							<div class="mb-3">
								<label for="titulo_banner_inicio" class="form-label">Título del Banner:</label>
								<input type="text"
									   id="titulo_banner_inicio"
									   name="titulo_banner_inicio"
									   class="form-control"
									   value="<?php echo htmlspecialchars($configData ? $configData->getTituloBannerInicio() ?? '' : ''); ?>"
									   placeholder="Ingrese el título principal del banner">
								<div class="form-text">Este será el título principal que aparece en el banner de la página de inicio.</div>
							</div>
							<?php #endregion TITULO BANNER FIELD ?>
						</div>
					</div>

					<div class="row">
						<div class="col-md-12">
							<?php #region region TEXTO BANNER FIELD ?>
							<div class="mb-3">
								<label for="texto_banner_inicio" class="form-label">Texto del Banner:</label>
								<textarea id="texto_banner_inicio"
										  name="texto_banner_inicio"
										  class="form-control"
										  rows="8"
										  placeholder="Ingrese el texto descriptivo del banner"><?php echo htmlspecialchars($configData ? $configData->getTextoBannerInicio() ?? '' : ''); ?></textarea>
								<div class="form-text">Texto descriptivo que aparece debajo del título en el banner.</div>
							</div>
							<?php #endregion TEXTO BANNER FIELD ?>
						</div>
					</div>

					<div class="row">
						<div class="col-md-12">
							<?php #region region VIDEO BANNER FIELD ?>
							<div class="mb-3">
								<label for="video_banner_inicio" class="form-label">Video de Fondo del Banner:</label>
								
								<?php if ($configData && $configData->getVideoBannerInicio()): ?>
									<div class="current-video mb-3">
										<div class="alert alert-info">
											<i class="fa fa-video me-2"></i>
											<strong>Video actual:</strong> <?php echo htmlspecialchars($configData->getVideoBannerInicio()); ?>
										</div>
									</div>
								<?php endif; ?>
								
								<input type="file"
									   id="video_banner_inicio"
									   name="video_banner_inicio"
									   class="form-control"
									   accept="video/webm,video/mp4,video/avi,video/mov,video/wmv">
								
								<div class="form-text">
									<strong>Recomendaciones:</strong><br>
									• <strong>Formato recomendado:</strong> .webm (mejor rendimiento web)<br>
									• <strong>Formatos permitidos:</strong> .webm, .mp4, .avi, .mov, .wmv<br>
									• <strong>Tamaño recomendado:</strong> 15MB<br>
									• <strong>Consejo:</strong> Archivos más pequeños mejoran la velocidad de carga<br>
								</div>
							</div>
							<?php #endregion VIDEO BANNER FIELD ?>
						</div>
					</div>

					<div class="row">
						<div class="col-12">
							<div class="alert alert-info">
								<i class="fa fa-info-circle"></i>
								<strong>Nota:</strong> Los cambios se reflejarán inmediatamente en la página de inicio. 
								El título y texto son opcionales, pero se recomienda completar ambos para una mejor experiencia de usuario.
							</div>
						</div>
					</div>
				</div>
				<div class="panel-footer text-end">
					<button type="submit" class="btn btn-primary">
						<i class="fa fa-save fa-fw me-1"></i> Guardar Banner
					</button>
				</div>
			</div>
		</form>
		<?php #endregion BANNER FORM ?>
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/core_js.view.php'; ?>

<script src="<?php echo RUTA_RESOURCES ?>js/dashboard.js"></script>

<script>
// Client-side validation for banner form
document.addEventListener('DOMContentLoaded', function() {
	const form = document.getElementById('banner-form');
	const videoInput = document.getElementById('video_banner_inicio');
	
	// File size validation
	videoInput.addEventListener('change', function() {
		const file = this.files[0];
		if (file) {
			const maxSize = 15 * 1024 * 1024; // 15MB
			if (file.size > maxSize) {
				alert('El archivo es demasiado grande. El tamaño máximo permitido es 15MB.');
				this.value = '';
				return;
			}
			
			// Show file info
			const fileInfo = `Archivo seleccionado: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
			console.log(fileInfo);
		}
	});
	
	// Form submission validation
	form.addEventListener('submit', function(e) {
		const titulo = document.getElementById('titulo_banner_inicio').value.trim();
		const texto = document.getElementById('texto_banner_inicio').value.trim();
		const video = document.getElementById('video_banner_inicio').files[0];
		
		// At least one field should be filled
		if (!titulo && !texto && !video) {
			e.preventDefault();
			alert('Por favor, complete al menos un campo para actualizar el banner.');
			return;
		}
		
		// Show loading state
		const submitBtn = this.querySelector('button[type="submit"]');
		const originalText = submitBtn.innerHTML;
		submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin fa-fw me-1"></i> Guardando...';
		submitBtn.disabled = true;
		
		// Re-enable button after 10 seconds as fallback
		setTimeout(() => {
			submitBtn.innerHTML = originalText;
			submitBtn.disabled = false;
		}, 10000);
	});
});
</script>

<?php #endregion JS ?>

</body>
</html>
