<?php
#region region DOCS

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en">

<head>
	<!-- ========== Meta Tags ========== -->
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="author" content="modinatheme">
	<!-- ======== Page title ============ -->
	<title><?php echo APP_NAME; ?> | Valores</title>
	
	<?php require_once __ROOT__ . '/views/web/head_section.view.php'; ?>
	
	<style>
        #listado_valores .icon {
            padding-bottom: 15px;
        }
	</style>
</head>

<body class="body-wrapper">
<!-- welcome content start from here -->
<!-- header__area start -->
<?php require_once __ROOT__ . '/views/web/header_menu_section.view.php'; ?>
<!-- header__area end -->

<main>
	<?php #region region form ?>
	<form action="valores" method="POST">
		<section id="valores" class="our-service-wrapper section-padding bg-gray pt-5">
			<div class="container">
				<div class="block-contents">
					<div class="section-title">
						<h2 class="wow fadeInUp2" data-wow-delay="0.4s">
							VALORES
						</h2>
					</div>
				</div>
				
				<div id="listado_valores" class="row mtm-40 mt-3">
					<?php if (!empty($valores)): ?>
						<?php
						$delay = 0.2; // Starting delay for animations
						foreach ($valores as $valor):
						?>
						<div class="col-xl-4 col-md-6 col-12">
							<div class="single-service-card wow fadeInUp2" data-wow-delay="<?php echo $delay; ?>s">
								<div class="content">
									<div class="icon-title">
										<div class="icon">
											<?php if ($valor->getImagen()): ?>
												<img src="<?php echo RUTA_RESOURCES; ?>images/valores/<?php echo htmlspecialchars($valor->getImagen()); ?>"
													 style="width: 80px; height: 80px; object-fit: contain;"
													 alt="<?php echo htmlspecialchars($valor->getTitulo()); ?>">
											<?php else: ?>
												<img src="<?php echo RUTA_RESOURCES; ?>images/valores/default.png"
													 style="width: 80px; height: 80px; object-fit: contain;"
													 alt="<?php echo htmlspecialchars($valor->getTitulo()); ?>">
											<?php endif; ?>
										</div>
										<div class="service-title">
											<h4><?php echo htmlspecialchars($valor->getTitulo()); ?></h4>
										</div>
									</div>
									<p>
										<?php echo nl2br(htmlspecialchars($valor->getTexto())); ?>
									</p>
								</div>
							</div>
						</div>
						<?php
						$delay += 0.1; // Increment delay for next item
						endforeach;
						?>
					<?php else: ?>
						<div class="col-12">
							<div class="text-center py-5">
								<h4 class="text-muted">No hay valores disponibles en este momento.</h4>
								<p class="text-muted">Por favor, vuelva más tarde.</p>
							</div>
						</div>
					<?php endif; ?>
				</div>
			</div>
		</section>
	</form>
	<?php #endregion form ?>
	
	<?php #region region footer ?>
	<?php require_once __ROOT__ . '/views/web/footer_section.view.php'; ?>
	<?php #endregion footer ?>
</main>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/web/js_section.view.php'; ?>

<?php #endregion JS ?>

</body>

</html>