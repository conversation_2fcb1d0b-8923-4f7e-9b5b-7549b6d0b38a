<?php
#region region DOCS

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en">

<head>
	<!-- ========== Meta Tags ========== -->
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="author" content="modinatheme">
	<!-- ======== Page title ============ -->
	<title><?php echo APP_NAME; ?> | Estrategias Corporativas</title>
	
	<?php require_once __ROOT__ . '/views/web/head_section.view.php'; ?>
	
	<link rel="stylesheet" href="<?php echo RUTA_RESOURCES ?>css/estrategias-corporativas.css">
</head>

<body class="body-wrapper">
<!-- welcome content start from here -->
<!-- header__area start -->
<?php require_once __ROOT__ . '/views/web/header_menu_section.view.php'; ?>
<!-- header__area end -->

<main>
	<?php #region region form ?>
	<form action="estrategias-corporativas" method="POST">
		<?php #region region estrategias ?>
		<section id="estrategias" class="why-choose-us__area why-choose-us-7 pt-5 pb-5" data-background="<?php echo RUTA_ASSETS_WEB ?>img/home-7/why-choose-us/bg-thumb.jpg">
			<div class="container container-1310">
				<div class="row">
					<div class="col-md-12 col-xs-12 text-center">
						<div class="section-title-2 ps-xl-4 text-white wow fadeInUp" data-wow-duration="1s" data-wow-delay="0.4s">
							<h1>Estrategias corporativas</h1>
						</div>
					</div>
				</div>
				<div class="row mt-3">
					<?php if (!empty($estrategias)): ?>
						<?php
						$estrategias_count = count($estrategias);
						$half_count = ceil($estrategias_count / 2);
						$left_column = array_slice($estrategias, 0, $half_count);
						$right_column = array_slice($estrategias, $half_count);
						?>

						<!-- Left Column -->
						<div class="col-md-6 col-xs-12">
							<?php foreach ($left_column as $index => $estrategia): ?>
								<div class="single-icon-circle-item wow fadeInUp2" data-wow-delay="<?php echo 0.3 + ($index * 0.2); ?>s">
									<div class="icon">
										<i class="flaticon-cpu"></i>
									</div>
									<div class="contents">
										<h3><?php echo htmlspecialchars($estrategia->getTitulo()); ?></h3>
										<p><?php echo htmlspecialchars($estrategia->getTexto()); ?></p>
									</div>
								</div>
							<?php endforeach; ?>
						</div>

						<!-- Right Column -->
						<div class="col-md-6 col-xs-12">
							<?php foreach ($right_column as $index => $estrategia): ?>
								<div class="single-icon-circle-item wow fadeInUp2" data-wow-delay="<?php echo 0.3 + (($index + $half_count) * 0.2); ?>s">
									<div class="icon">
										<i class="flaticon-cpu"></i>
									</div>
									<div class="contents">
										<h3><?php echo htmlspecialchars($estrategia->getTitulo()); ?></h3>
										<p><?php echo htmlspecialchars($estrategia->getTexto()); ?></p>
									</div>
								</div>
							<?php endforeach; ?>
						</div>
					<?php else: ?>
						<!-- Fallback content if no strategies are found -->
						<div class="col-12 text-center">
							<p class="text-muted">No hay estrategias disponibles en este momento.</p>
						</div>
					<?php endif; ?>
				</div>
			</div>
		</section>
		<?php #endregion estrategias ?>
	</form>
	<?php #endregion form ?>
	
	<?php #region region footer ?>
	<?php require_once __ROOT__ . '/views/web/footer_section.view.php'; ?>
	<?php #endregion footer ?>
</main>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/web/js_section.view.php'; ?>

<?php #endregion JS ?>

</body>

</html>
