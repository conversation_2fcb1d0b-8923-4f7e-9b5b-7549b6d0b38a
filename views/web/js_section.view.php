<!--  ALl JS Plugins====================================== -->
<script src="<?php echo RUTA_ASSETS_WEB ?>js/jquery.min.js"></script>
<script src="<?php echo RUTA_ASSETS_WEB ?>js/bootstrap.bundle.min.js"></script>
<script src="<?php echo RUTA_ASSETS_WEB ?>js/meanmenu.js"></script>
<script src="<?php echo RUTA_ASSETS_WEB ?>js/jquery.easing.js"></script>
<script src="<?php echo RUTA_ASSETS_WEB ?>js/isotope.pkgd.min.js"></script>
<script src="<?php echo RUTA_ASSETS_WEB ?>js/imageload.min.js"></script>
<script src="<?php echo RUTA_ASSETS_WEB ?>js/waypoints.min.js"></script>
<script src="<?php echo RUTA_ASSETS_WEB ?>js/odometer.min.js"></script>
<script src="<?php echo RUTA_ASSETS_WEB ?>js/owl.carousel.min.js"></script>
<script src="<?php echo RUTA_ASSETS_WEB ?>js/slick.min.js"></script>
<script src="<?php echo RUTA_ASSETS_WEB ?>js/magnific-popup.min.js"></script>
<script src="<?php echo RUTA_ASSETS_WEB ?>js/counterup.min.js"></script>
<script src="<?php echo RUTA_ASSETS_WEB ?>js/wow.min.js"></script>
<script src="<?php echo RUTA_ASSETS_WEB ?>js/scrollUp.min.js"></script>
<script src="<?php echo RUTA_ASSETS_WEB ?>js/ajax-mail.js"></script>
<script src="<?php echo RUTA_ASSETS_WEB ?>js/active.js"></script>

<!-- custom JS -->
<script src="<?php echo RUTA_RESOURCES ?>js/sidebar_menu_enhancement.js"></script>

<?php #region region JS sweet alert import ?>
<script src="<?php echo RUTA ?>resources/sweetalert/dist/sweetalert.min.js"></script>
<?php #endregion JS sweet alert import ?>
<?php #region region JS sweet alert function ?>
<script>
    function showSweetAlertSuccess(title, message) {
        swal({
            title : title,
            text  : message,
            icon  : 'success',
            button: {
                text: "Ok", // Texto del botón
                value: true, // Valor devuelto en la promesa .then()
                visible: true,
                className: "btn-success", // <-- Clase CSS (Bootstrap para verde)
                closeModal: true
            }
        });
    }
    
    function showSweetAlertError(title, message) {
        swal({
            title : title,
            text  : message,
            icon  : 'error',
            button: {
                text: "Cerrar",
                value: null, // O false si quieres diferenciarlo en .then()
                visible: true,
                className: "btn-danger", // <-- Clase CSS (Bootstrap para rojo)
                closeModal: true
            }
        });
    }
</script>
<?php #endregion JS sweet alert function ?>
<?php #region region JS sweet alert success ?>
<?php if (!empty($success_text)): ?>
	<script type="text/javascript">
        showSweetAlertSuccess("<?php echo $success_text; ?>");
	</script>
<?php endif; ?>
<?php #endregion JS sweet alert success ?>
<?php #region region JS sweet alert error ?>
<?php if (!empty($error_text)): ?>
	<script type="text/javascript">
        showSweetAlertError("<?php echo $error_text; ?>");
	</script>
<?php endif; ?>
<?php #endregion JS sweet alert error ?>
<?php #region region JS smooth transition to section with some pixels above ?>
<script>
    // Renamed function: Navigates to a URL and sets the section ID for scrolling
    function navigateToSection(targetUrl, sectionId) {
        sessionStorage.setItem("scrollToSection", sectionId); // Store the section ID
        window.location.href = targetUrl; // Navigate to the target page URL
    }
    
    // This part remains the same, but MUST be included on the TARGET page(s)
    // (e.g., your index.html, or wherever 'serviciostec' exists)
    window.addEventListener('load', function () { // Using addEventListener is generally better than onload
        const sectionId = sessionStorage.getItem("scrollToSection");
        if (sectionId) {
            sessionStorage.removeItem("scrollToSection"); // Clear after using
            const section = document.getElementById(sectionId);
            if (section) {
                // Using requestAnimationFrame might help ensure rendering is ready
                requestAnimationFrame(() => {
                    setTimeout(() => { // Keep timeout for potential layout shifts after load
                        const yOffset = -250; // Your desired offset
                        const y       = section.getBoundingClientRect().top + window.scrollY + yOffset;
                        window.scrollTo({top: y, behavior: "smooth"});
                    }, 100); // Keep your original delay or adjust if needed
                });
            } else {
                console.warn("Scroll target section not found:", sectionId); // Good for debugging
            }
        }
    });
</script>
<?php #endregion JS smooth transition to section with some pixels above ?>

